"""
Update Data view implementation.
"""

import os

import pandas as pd
from PySide6.QtCore import Signal
from PySide6.QtWidgets import QMessageBox

from ...base.base_module_view import BaseModuleView
from .ud_view_widgets.btns_left_panel import LeftPanelButtonsWidget
from .ud_view_widgets.center_display import CenterDisplay


class UpdateDataView(BaseModuleView):
    """Update Data view."""
    
    #------------------
    # Signals
    #------------------
    source_select_requested = Signal(str)
    save_select_requested = Signal()
    process_clicked = Signal()
    cancel_clicked = Signal()
    source_option_changed = Signal(str)
    save_option_changed = Signal(str)
    
    def __init__(self, parent=None):
        """Initialize the view."""
        super().__init__(parent)
    
    def setup_ui(self):
        """Initial UI setup - called by base class."""
        # Create widgets
        self.left_buttons = LeftPanelButtonsWidget()
        self.center_display = CenterDisplay()
        self._connect_signals()
    
    def _connect_signals(self):
        """Connect internal signals to be forwarded."""
        # Forward all signals from buttons widget
        self.left_buttons.source_select_requested.connect(self.source_select_requested.emit)
        self.left_buttons.save_select_requested.connect(self.save_select_requested.emit)
        self.left_buttons.process_clicked.connect(self.process_clicked.emit)
        self.left_buttons.cancel_clicked.connect(self.cancel_clicked.emit)
        self.left_buttons.source_option_changed.connect(self.source_option_changed.emit)
        self.left_buttons.save_option_changed.connect(self.save_option_changed.emit)
    
    def setup_left_panel(self, layout):
        """Set up the left panel with control buttons."""
        layout.addWidget(self.left_buttons)
    
    def setup_center_panel(self, layout):
        """Set up the center panel with display areas."""
        layout.addWidget(self.center_display)
    
    def disconnect_signals(self):
        """Clean up signal connections."""
        if hasattr(self, 'left_buttons'):
            self.left_buttons.source_select_requested.disconnect()
            self.left_buttons.save_select_requested.disconnect()
            self.left_buttons.process_clicked.disconnect()
            self.left_buttons.cancel_clicked.disconnect()
    
    #------------------
    # Public Interface
    #------------------
    def set_exit_mode(self):
        """Set left panel to exit mode."""
        self.left_buttons.set_exit_mode()
        
    def set_process_mode(self):
        """Set left panel to process mode."""
        self.left_buttons.set_process_mode()
    
    def display_selected_source(self, source_info: dict):
        """Display the selected source files in the center panel."""
        if not source_info:
            return
            
        if source_info["type"] == "folder":
            self.center_display.set_source_path(source_info["path"])
            files = [os.path.basename(f) for f in source_info["file_paths"]]
            self.center_display.set_files(files)
        else:  # files
            files = source_info["paths"]
            self.center_display.set_source_path(os.path.dirname(files[0]))
            self.center_display.set_files([os.path.basename(f) for f in files])
    
    def display_master_csv(self, df: pd.DataFrame):
        """Display a DataFrame in the center panel table."""
        self.center_display.display_master_csv(df)
    
    def display_welcome(self):
        """Display welcome message in center panel."""
        self.center_display.display_welcome()
    
    def show_error(self, message: str, title: str = "Error"):
        """Show error message box."""
        QMessageBox.critical(self, title, message)
    
    def show_success(self, message: str, title: str = "Success"):
        """Show success message box."""
        QMessageBox.information(self, title, message)

    def set_save_select_enabled(self, enabled: bool):
        """Enable/disable save select button."""
        self.left_buttons.set_save_select_enabled(enabled)
