# Update Data Module Improvements

## Current Issues

### 1. File Path Display Issues
- **Status Bar Visibility**: The status bar at the bottom of the center panel displays selected directories in dark grey text that is difficult to read
- **Missing File Paths**: No file paths appear under the "Source Folder" and "Save Location" labels
- **Empty Text Box**: The text box under "Selected Files" label is empty

### 2. Button Layout and Redundancy
- **Duplicate Buttons**: There are two "Remove Selected" buttons
- **Button Placement**: "Add Files..." button placement could be improved

## Proposed Improvements

### 1. Improve File Path Display
- [ ] Increase contrast for the status bar text (change from dark grey to a more readable color)
- [ ] Ensure file paths are properly displayed under the "Source Folder" and "Save Location" labels
- [ ] Add proper styling to make file paths stand out visually
- [ ] Consider adding tooltips for long paths that may be truncated

### 2. Enhance Selected Files Display
- [ ] Ensure the text box under "Selected Files" properly displays the selected files
- [ ] Add file icons to improve visual identification
- [ ] Consider adding file size and type information
- [ ] Implement proper scrolling for long lists of files

### 3. Reorganize Button Layout
- [ ] Remove duplicate "Remove Selected" button
- [ ] Group related buttons together (e.g., "Add Files" and "Remove Selected")
- [ ] Consider adding keyboard shortcuts for common actions
- [ ] Add visual feedback when buttons are clicked

### 4. General UI Improvements
- [ ] Add visual feedback when files are successfully selected
- [ ] Improve error messaging for invalid selections
- [ ] Consider adding drag-and-drop support for file selection
- [ ] Ensure consistent styling across all components

## Code Structure Improvements

### 1. Event System Standardization
- [x] Standardize on pub/sub terminology throughout the module
- [x] Rename signal connection methods to use subscribe/publish terminology
- [x] Improve documentation of the event flow
- [ ] Consider adding typed events instead of using string-based event names

### 2. Component Responsibility Clarification
- [x] Integrate the info widget directly into the center display
- [ ] Clarify the relationship between FileDisplayWidget and CenterDisplay
- [ ] Ensure each widget has a single, clear responsibility
- [ ] Remove redundant code and consolidate similar functionality

### 3. Resource File Handling
- [ ] Investigate resource file errors in backups
- [ ] Ensure all necessary resource files are included in backups
- [ ] Add proper error handling for missing resource files

## Implementation Priority
1. Fix the file path display issues (highest priority)
2. Fix the duplicate button issue
3. Enhance the selected files display
4. Complete the code structure improvements
5. Implement general UI improvements

## Related Files
- `ud_view.py`: Main view implementation
- `ud_presenter.py`: Presenter implementation
- `center_display.py`: Center display widget implementation
- `file_display_widget.py`: File display widget implementation
- `info_widget.py`: Status and progress display widget
- `services/events.py`: Event service for the module
