#!/usr/bin/env python3
"""
Schema Compliance Validator

Validates that the test GUI implementation correctly follows the MVP state schema
as defined in the Excel file. This script performs automated checks to ensure
all components, states, and transitions work as specified.

Usage:
    python validate_schema_compliance.py
"""

import sys
import csv
from pathlib import Path
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass

# Setup Python path
project_root = Path(__file__).parent.parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import QTimer
    from test_schema_gui import SchemaTestGUI
    from select_group_widget import SelectGroupWidget, SelectGroupConfig
    from state_engine import StateEngine, UIState
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running from the correct directory with dependencies installed.")
    sys.exit(1)


@dataclass
class ValidationResult:
    """Result of a validation check."""
    test_name: str
    passed: bool
    message: str
    details: str = ""


class SchemaValidator:
    """
    Validates schema compliance of the test GUI implementation.
    
    Performs automated checks to ensure:
    - All schema components are present
    - Widget types match schema specifications
    - State transitions work correctly
    - Component behavior matches schema definitions
    """
    
    def __init__(self):
        self.app = None
        self.gui = None
        self.schema_data = {}
        self.validation_results: List[ValidationResult] = []
        
    def load_schema(self, csv_path: str) -> bool:
        """Load schema data from CSV file."""
        try:
            with open(csv_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    component_id = row.get('component_id', '').strip()
                    if component_id and not component_id.startswith('#') and component_id != '':
                        self.schema_data[component_id] = row
            
            print(f"✓ Loaded {len(self.schema_data)} components from schema")
            return True
            
        except Exception as e:
            print(f"❌ Failed to load schema: {e}")
            return False
    
    def setup_test_environment(self) -> bool:
        """Setup the test environment with GUI instance."""
        try:
            self.app = QApplication.instance()
            if self.app is None:
                self.app = QApplication(sys.argv)
            
            self.gui = SchemaTestGUI()
            print("✓ Test GUI created successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to setup test environment: {e}")
            return False
    
    def validate_component_presence(self) -> ValidationResult:
        """Validate that all schema components are present in the GUI."""
        missing_components = []
        present_components = []
        
        for component_id in self.schema_data:
            if hasattr(self.gui, component_id):
                present_components.append(component_id)
            else:
                missing_components.append(component_id)
        
        if missing_components:
            return ValidationResult(
                test_name="Component Presence",
                passed=False,
                message=f"Missing components: {', '.join(missing_components)}",
                details=f"Present: {len(present_components)}, Missing: {len(missing_components)}"
            )
        else:
            return ValidationResult(
                test_name="Component Presence",
                passed=True,
                message=f"All {len(present_components)} schema components present",
                details=f"Components: {', '.join(present_components)}"
            )
    
    def validate_widget_types(self) -> ValidationResult:
        """Validate that widget types match schema specifications."""
        type_mismatches = []
        correct_types = []
        
        for component_id, schema_row in self.schema_data.items():
            if not hasattr(self.gui, component_id):
                continue
                
            widget = getattr(self.gui, component_id)
            expected_base_class = schema_row.get('base_class', '')
            
            # Check if widget class name matches expected base class
            actual_class = widget.__class__.__name__
            
            if expected_base_class in actual_class or actual_class == expected_base_class:
                correct_types.append(f"{component_id}: {actual_class}")
            else:
                type_mismatches.append(f"{component_id}: expected {expected_base_class}, got {actual_class}")
        
        if type_mismatches:
            return ValidationResult(
                test_name="Widget Types",
                passed=False,
                message=f"Type mismatches found: {len(type_mismatches)}",
                details=f"Mismatches: {'; '.join(type_mismatches)}"
            )
        else:
            return ValidationResult(
                test_name="Widget Types",
                passed=True,
                message=f"All {len(correct_types)} widget types correct",
                details=f"Correct types: {'; '.join(correct_types)}"
            )
    
    def validate_initial_states(self) -> ValidationResult:
        """Validate that components have correct initial states."""
        state_errors = []
        correct_states = []
        
        for component_id, schema_row in self.schema_data.items():
            if not hasattr(self.gui, component_id):
                continue
                
            widget = getattr(self.gui, component_id)
            expected_state = schema_row.get('state_initial', '')
            
            # Check visibility
            if expected_state == 'hidden':
                if widget.isVisible():
                    state_errors.append(f"{component_id}: should be hidden but is visible")
                else:
                    correct_states.append(f"{component_id}: correctly hidden")
            elif expected_state == 'visible':
                if not widget.isVisible():
                    state_errors.append(f"{component_id}: should be visible but is hidden")
                else:
                    correct_states.append(f"{component_id}: correctly visible")
            
            # Check enabled state
            if expected_state == 'disabled':
                if widget.isEnabled():
                    state_errors.append(f"{component_id}: should be disabled but is enabled")
                else:
                    correct_states.append(f"{component_id}: correctly disabled")
            elif expected_state in ['active', 'visible']:
                if not widget.isEnabled():
                    state_errors.append(f"{component_id}: should be enabled but is disabled")
                else:
                    correct_states.append(f"{component_id}: correctly enabled")
        
        if state_errors:
            return ValidationResult(
                test_name="Initial States",
                passed=False,
                message=f"State errors found: {len(state_errors)}",
                details=f"Errors: {'; '.join(state_errors)}"
            )
        else:
            return ValidationResult(
                test_name="Initial States",
                passed=True,
                message=f"All {len(correct_states)} initial states correct",
                details=f"Correct states: {'; '.join(correct_states)}"
            )
    
    def validate_initial_text(self) -> ValidationResult:
        """Validate that components have correct initial text."""
        text_errors = []
        correct_text = []
        
        for component_id, schema_row in self.schema_data.items():
            if not hasattr(self.gui, component_id):
                continue
                
            widget = getattr(self.gui, component_id)
            expected_text = schema_row.get('text_initial', '')
            
            if not expected_text:
                continue
                
            # Get actual text from widget
            actual_text = ""
            if hasattr(widget, 'text'):
                actual_text = widget.text()
            elif hasattr(widget, 'getText'):
                actual_text = widget.getText()
            elif hasattr(widget, 'get_text'):
                actual_text = widget.get_text()
            
            if actual_text == expected_text:
                correct_text.append(f"{component_id}: '{actual_text}'")
            else:
                text_errors.append(f"{component_id}: expected '{expected_text}', got '{actual_text}'")
        
        if text_errors:
            return ValidationResult(
                test_name="Initial Text",
                passed=False,
                message=f"Text errors found: {len(text_errors)}",
                details=f"Errors: {'; '.join(text_errors)}"
            )
        else:
            return ValidationResult(
                test_name="Initial Text",
                passed=True,
                message=f"All {len(correct_text)} text values correct",
                details=f"Correct text: {'; '.join(correct_text)}"
            )
    
    def validate_select_group_widgets(self) -> ValidationResult:
        """Validate SelectGroupWidget implementation."""
        errors = []
        successes = []
        
        # Check if SelectGroupWidget components exist
        select_group_components = ['source_group', 'save_group']
        
        for component_id in select_group_components:
            if hasattr(self.gui, component_id):
                widget = getattr(self.gui, component_id)
                
                if isinstance(widget, SelectGroupWidget):
                    successes.append(f"{component_id}: correct SelectGroupWidget type")
                    
                    # Test SelectGroupWidget API
                    try:
                        # Test basic methods
                        current_value = widget.value()
                        widget.setValue("test")
                        widget.set_button_enabled(False)
                        widget.set_button_enabled(True)
                        
                        successes.append(f"{component_id}: API methods working")
                        
                    except Exception as e:
                        errors.append(f"{component_id}: API error - {e}")
                        
                else:
                    errors.append(f"{component_id}: expected SelectGroupWidget, got {type(widget).__name__}")
            else:
                errors.append(f"{component_id}: SelectGroupWidget not found")
        
        if errors:
            return ValidationResult(
                test_name="SelectGroupWidget",
                passed=False,
                message=f"SelectGroupWidget errors: {len(errors)}",
                details=f"Errors: {'; '.join(errors)}"
            )
        else:
            return ValidationResult(
                test_name="SelectGroupWidget",
                passed=True,
                message=f"SelectGroupWidget validation passed: {len(successes)} checks",
                details=f"Successes: {'; '.join(successes)}"
            )
    
    def validate_state_engine(self) -> ValidationResult:
        """Validate state engine functionality."""
        if not hasattr(self.gui, 'state_engine') or self.gui.state_engine is None:
            return ValidationResult(
                test_name="State Engine",
                passed=False,
                message="State engine not initialized",
                details="GUI should have a state_engine attribute"
            )
        
        state_engine = self.gui.state_engine
        
        try:
            # Test basic state engine functionality
            current_state = state_engine.get_current_state()
            
            # Test component registration
            registered_components = len(state_engine.component_widgets)
            
            return ValidationResult(
                test_name="State Engine",
                passed=True,
                message=f"State engine working - {registered_components} components registered",
                details=f"Current state: {current_state.value}, Components: {registered_components}"
            )
            
        except Exception as e:
            return ValidationResult(
                test_name="State Engine",
                passed=False,
                message=f"State engine error: {e}",
                details="State engine failed basic functionality test"
            )
    
    def run_all_validations(self) -> List[ValidationResult]:
        """Run all validation checks."""
        print("\n" + "=" * 60)
        print("Running Schema Compliance Validation")
        print("=" * 60)
        
        validations = [
            self.validate_component_presence,
            self.validate_widget_types,
            self.validate_initial_states,
            self.validate_initial_text,
            self.validate_select_group_widgets,
            self.validate_state_engine
        ]
        
        results = []
        for validation_func in validations:
            try:
                result = validation_func()
                results.append(result)
                
                status = "✓ PASS" if result.passed else "❌ FAIL"
                print(f"{status} {result.test_name}: {result.message}")
                
                if result.details and not result.passed:
                    print(f"    Details: {result.details}")
                    
            except Exception as e:
                error_result = ValidationResult(
                    test_name=validation_func.__name__,
                    passed=False,
                    message=f"Validation error: {e}",
                    details=""
                )
                results.append(error_result)
                print(f"❌ ERROR {validation_func.__name__}: {e}")
        
        return results
    
    def print_summary(self, results: List[ValidationResult]):
        """Print validation summary."""
        passed = sum(1 for r in results if r.passed)
        total = len(results)
        
        print("\n" + "=" * 60)
        print("VALIDATION SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 ALL VALIDATIONS PASSED!")
            print("The schema implementation is compliant.")
        else:
            print(f"\n⚠️  {total - passed} VALIDATIONS FAILED")
            print("Review the failed tests above for details.")
        
        print("=" * 60)


def main():
    """Main validation entry point."""
    # Setup paths - adjust for correct project structure
    schema_csv_path = project_root / "DOCS" / "_REFACTORING_workspace" / "UI_REFACTOR_UPDATE_DATA" / "Schema" / "mvp_state_schema.csv"
    
    if not schema_csv_path.exists():
        print(f"❌ Schema CSV not found: {schema_csv_path}")
        return 1
    
    # Create validator
    validator = SchemaValidator()
    
    # Load schema
    if not validator.load_schema(str(schema_csv_path)):
        return 1
    
    # Setup test environment
    if not validator.setup_test_environment():
        return 1
    
    # Run validations
    results = validator.run_all_validations()
    
    # Print summary
    validator.print_summary(results)
    
    # Return exit code based on results
    failed_count = sum(1 for r in results if not r.passed)
    return 0 if failed_count == 0 else 1


if __name__ == "__main__":
    sys.exit(main())
