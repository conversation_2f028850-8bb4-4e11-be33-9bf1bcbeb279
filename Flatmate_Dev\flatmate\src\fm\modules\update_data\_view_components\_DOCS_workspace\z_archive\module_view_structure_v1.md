# Module View Structure (v1)

This document outlines the standardized structure for module view components in the FlatMate application. This structure is designed to be intuitive, maintainable, and easily navigable by developers.

## Directory Structure

```
_view/
  ├── common/
  │   └── base_panel_manager.py       (Base class for all panels) # base_panel_manager ? could be .. panel_main even 
  │   └── base_pane.py #?       (Base class for all panes) #? what base modules would be useful/worth building?
  ├── left_panel/
  │   ├── _panel_manager.py       (Main action and navigation panel) This should contain most common operations in general order of use 
  │   └── widgets/            (Widgets specific to the left panel)
  │       └──button.py   (Navigation button widget) #? Does this contain all the buttons in seperate files? or all the buttons in one file? Where are the labels and so on defined ? 
  │
  ├── center_panel/
  │   ├── _panel_manager.py        (Main controller for center panel)
  │   ├── switcher.py         (Panel switching mechanism)
  │   ├── welcome_pane.py     (Welcome screen)
  │   ├── file_pane.py        (File management panel)
  │   ├── data_pane.py        (Data display panel)
  │   └── widgets/            (Widgets specific to center panel)
  │       ├── file_browser.py (File browser widget)
  │       ├── status_bar.py   (Status display widget)
  │       ├── action_button.py (Button widget)
  │       └── info_display.py (Information display widget)
  │
  ├── right_panel/            (Future right panel for settings)
  │   ├── _panel_manager.py         (Settings panel)
  |   ├── options_pane.py
  │   └── widgets/            (Widgets specific to the right panel)
  │       └── settings_control.py (Settings control widget)
  │
  └── utils/
      └── file_helper.py      (Utility functions)
```

## Key Concepts

### Panel Organization

- Each major UI region (left, center, right) has its own directory
- Each panel directory contains:
  - Top-level panel components (directly in the panel directory)
  - A widgets subdirectory for components used by those panels

### Component Hierarchy

- **Panels**: Top-level containers that define major UI areas
- **Panes**: Specific screens or views within a panel (like welcome_pane.py)
- **Widgets**: Reusable UI components used by panels or panes
- **Widget Files**: as a rule where have multiple widgetes of a single type - say buttons - we will put them in one file, whithin that panel folder
### Naming Conventions

- Files are named according to their function (navigation.py, file_pane.py)
- Special controller components use an underscore prefix (_director.py)
- Widget files clearly indicate their purpose (file_browser.py, status_bar.py)

### Event System

- We use publish/subscribe terminology for the event system
- Signal names use the "publish_" prefix (e.g., publish_file_selected)
- Connection methods use "subscribe_to_" prefix when appropriate

## Benefits

1. **Intuitive Navigation**: Easy to find components based on their location in the UI
2. **Clear Ownership**: Widgets are associated with the panels that use them
3. **Standardized Pattern**: Consistent structure across all modules
4. **Modular and Expandable**: Easy to add new panels or widgets
5. **Reusable Components**: Common base classes promote code reuse

This structure serves as a template for all modules that inhabit the main window, with each module following the same pattern but with their specific panels and widgets.

## Development notes

We may want a nav control in a top bar of the center panel, this would have shortcits for the main modules in it .. Home, Profiles, Accounts, Data (update data) 
I'm not sure at this stage ... certainly there should be a small settings and or tools icon in the top right .... Either that or an options button in the left..
I'm still deciding... this is tricky...
I think for now it would be ideal to instigate this structure, it will the be easier to make changes. 
There is some work to do to get things systematic and consistent, for instance, I have no idea where the left panel labels are defined currently..
They are probably in view...

## widget styling, core GUI 
An important thing is, we want consistent styling across modules ..
So best we have a design language, and consistent widget formats ..
etc We have to consider what widgets and so on are bespoke and which should be used throughout ..
We can test them here and they can become standard widgets if theyre good.
Where possible we should use elements we have already created in the app fm/src/gui folder QT qss files 
This should contain common elements:

action buttons
select buttons
exit buttons
main panel titles
select labels
info_text (label?) perhaps we should reffer tot he actual widget name if we are unsure what to call something .. 

At the moment I belive these are only in UD data module:
info display
file browser 
Add / remove buttons 

We may also want - center panel buttons ... they mey want to be different .. or pane buttons ..
We should think about where icons might be more appropriate than text

We should also think about - the icon set .. either feather or... I should have a look atwhat we have already..its been a while, I did find a set I liked 

## Stand alone
at some point we may want to instigate a stand alone folder for each module - this would be, everything we need from the mothership .. to run the module independantly.. as a simple statement merger app... 


q "Well the thing is, we have a core gui folder ..
in src/fm
this has the main window, and has the qss files etc... we'd need to define what should go where ...

AI: You raise an important point about the relationship between the module-specific structure and the core GUI components. Let's clarify what should go where:

Core GUI vs Module-Specific Components
Core GUI (src/fm/gui/):

Main application window
Global theme and styling (QSS files)
Application-wide components (toolbar, status bar)
Shared widgets used across multiple modules
Global navigation between modules
Theme management
Dialog templates
Module-Specific (src/fm/modules/update_data/_view/):

Module-specific panels and panes
Components that are only used within this module
Module-specific layout and navigation
Specialized widgets for this module's functionality
For components that could belong in either place, we should follow these principles:

If it's used by multiple modules → Core GUI
If it's specific to one module → Module _view
If it's a specialized version of a core component → Module _view, possibly inheriting from a core component
Common Folder Considerations
Given this distinction, the common folder in our module structure should primarily contain:

Base classes specific to this module
Utilities specific to this module's view
Module-specific extensions of core components
Components that might be useful across modules should be considered for promotion to the core GUI folder.

This approach keeps the module structure focused on its specific needs while leveraging the core GUI for shared functionality. It also makes it clearer where to look for different types of components."


In this respect, once we have designed widget implementations that are good enough, and have other modules tha need them, we can promote them to src/gui folder  
I am currently wonderinf if all this stuff goes in core... anyway, thats a question for another day..
Lets get this new structure in place ....