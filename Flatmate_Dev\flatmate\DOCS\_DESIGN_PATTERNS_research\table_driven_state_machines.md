# Design Pattern: Table-Driven State Machine

**Date**: 2025-07-23
**Author**: Cascade

## 1. Concept

A table-driven state machine is a design pattern where the logic for state transitions is defined in an external, table-like data structure (e.g., a CSV, JSON file, or a database table) rather than being hard-coded into the application using conditional logic like `if/else` or `match/case` statements.

A generic state engine reads this table at runtime to determine how to transition from one state to another based on incoming events.

## 2. How It Works

The core of the pattern is a state transition table. At runtime, the engine's logic is straightforward:

1.  It maintains the `current_state`.
2.  It receives an `event` (e.g., a user clicking a button, a timer expiring, data arriving).
3.  It performs a lookup in the state table using the `(current_state, event)` pair as a key.
4.  The corresponding entry in the table provides the `next_state` to transition to and the `action(s)` to execute.

### Example State Table:

| Current State      | Event/Trigger              | Next State         | Action(s) to Execute                             |
| :----------------- | :------------------------- | :----------------- | :----------------------------------------------- |
| `INITIAL`          | `dropdown_changed_to_files` | `FILES_MODE`       | `update_dropdown_text`, `show_info_pane`         |
| `INITIAL`          | `select_button_clicked`    | `(no change)`      | `open_folder_dialog`                             |
| `(any)`            | `folder_selected`          | `FOLDER_SELECTED`  | `update_button_text`, `show_auto_import_checkbox` |

## 3. Implementation Notes

- **Performance:** To negate any performance impact from file I/O, the state table should be loaded into an in-memory data structure (e.g., a Python dictionary or a hash map) at application startup. For UI-driven events, the lookup time for a hash map is negligible and will not impact user experience.

## 4. Advantages

- **Flexibility & Maintainability**: The application's flow can be modified by simply editing the external table, without changing the core application code. This is extremely powerful for systems with complex or frequently changing logic.
- **Clarity & Readability**: The state logic is declarative and centralised in one place, making it easy to understand, review, and debug. The table itself serves as formal documentation for the system's behaviour.
- **Scalability**: This pattern scales very well. Managing hundreds of states and transitions is far more tractable in a table than in deeply nested conditional code blocks.

## 5. Disadvantages

- **Initial Complexity**: Implementing the generic state transition engine requires more upfront design and effort than hard-coding the logic for a simple state machine with few states.
- **Tooling**: Debugging can sometimes be less direct, as you are debugging the engine and its data rather than stepping through explicit conditional code.

## 6. When to Use

- For systems with a large number of states or complex, branching logic.
- When the state logic is expected to change frequently.
- When non-programmers (e.g., UX designers, business analysts) need to be able to review or modify the application's behaviour.

For simple cases with a small, fixed number of states (like the 'Select Source' UI), a hard-coded implementation within a ViewModel is often clearer and more direct. However, for more complex workflows, the table-driven approach is a superior architectural choice.
