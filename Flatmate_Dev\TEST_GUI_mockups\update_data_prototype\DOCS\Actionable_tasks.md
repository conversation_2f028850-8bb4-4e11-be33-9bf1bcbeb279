# Test Tasks / To-Do List

> Update this list after each test cycle with distilled, actionable items from the test report.

## Current Actionable Tasks

- [ ] Make Process button state visually unambiguous (active/inactive)
- [ ] Info pane should nudge user to set a default import folder (optionally offer "Set as default")
- [ ] Folder selection dialog: clarify "No Items Match your search" (reduce user confusion)
- [ ] Display folder path in a way that is copyable or shortened with tooltip
- [ ] Change "Source Folder" label to something more user-friendly
- [ ] Ensure button text and state ("Change…", "Select…") is always clear

---
*Add/remove tasks as needed after each cycle. Completed tasks should be checked off and dated.*
