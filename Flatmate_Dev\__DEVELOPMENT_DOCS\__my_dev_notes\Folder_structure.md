_ai_suggestions.md

# proposed gui folder restructuring:

- [link](/flatmate/src/fm/gui) 

## v1



src/fm/
├── core/                  # Core application logic
├── gui/
│   ├── global/           # Global UI components
│   │   ├── nav_pane/     # Navigation
│   │   ├── side_bar/     # Sidebars
│   │   └── status_bar/   # Status components
│   ├── modules/          # Module-specific UI
│   └── shared/           # Shared UI components
└── modules/              # Business logic modules

## v2

src/fm/
├── core/                       # System-wide services & utilities
│   ├── config/                # Core config system (singleton + local abstractions)
│   │   ├── README.md         # Config system overview and usage
│   │   ├── config.py         # Core singleton config manager
│   │   ├── base_local_config.py  # Component config abstraction (was modules/base/config/)
│   │   ├── keys.py           # Centralized config key definitions
│   │   └── paths.py          # Path management utilities
│   ├── event_bus.py          # Event system
│   ├── logger.py             # Logging
│   └── services/             # Core services
│
├── gui/
│   ├── main_window/               # App-wide UI components owned by main_window
│   │   ├── nav_pane/        # Navigation (from gui/components/nav_pane.py)
│   │   │   ├── nav_pane.py
│   │   │   └── nav_buttons.py
│   │   ├── info_bar/        # Current info_bar implementation
│   │   └── settings/        # Global settings panel
│   │
│   ├── shared/              # Reusable UI components
│   │   ├── base_widgets.py  # From current base_widgets.py
│   │   └── panels/         # Common panel implementations
│   │
│   └── modules/            # Module-specific UI components
│       └── update_data/    # Update data module UI
│

### probably move UD base classes to gui components shared base folder
└── modules/               # Business logic & module coordination
    ├── base/             # Base module classes
    ├── home/            
    └── update_data/      # Update data module logic


## gui shared detail 

gui/components/shared/
├── __init__.py
├── mixins/
│   ├── __init__.py
│   ├── panel_mixins.py      # Activation, signals, state
│   └── widget_mixins.py     # Common widget behaviors
├── base/
│   ├── __init__.py
│   └── panels.py           # Basic panel implementations
└── widgets/
    ├── __init__.py
    └── common_widgets.py   # Reusable widgets


 ##   Action plan work through UD data and adapt to new system

 base/ (from _common/)
├── base_pane.py         -> base/panels.py
└── base_panel_manager.py -> base/panel_manager.py

mixins/
└── panel_mixins.py      # New file with activation/state behavior
Common Widgets to Extract:
CopyInsert
widgets/
├── info_bar.py        # From ud_info_bar.py
├── status_bar.py      # From ud_status_bar.py
└── file_browser.py    # From file_browser.py if generic enough
Keep in update_data:
CopyInsert
update_data/_view/
├── panels/
│   ├── center/
│   │   ├── data_panel.py
│   │   ├── file_panel.py
│   │   └── welcome_panel.py
│   ├── left/
│   │   └── options_panel.py
│   └── right/
│       └── settings_panel.py
└── widgets/           # Module-specific widgets



- questions: where does info_bar_service go?
- what use case is there for module specific UI components? surely they should go in the modules themselves
- is "global" the best name - the easisest way to implement has been to put them in main_window itself (in this case import)
- how would this relate to my idea about a container for everything in a given main_window element?: does this idea have merit?
- it looks like we are really talking about /gui/*
- core looks th same, am I correct?
- what defines a service in core/service ? arent event_bus and logger.py services?

# core detailed proposal, typical example:
[link](/flatmate/src/fm/core)

core/
├── domain/           # Core business entities & value objects
│   └── models/      # Domain models (e.g., Flatmate, Bill, etc.)
├── exceptions/       # Custom exceptions
├── interfaces/       # Abstract base classes & protocols
├── services/        # Application services
├── utils/           # Utility functions & helpers
└── constants/       # Core constants & enums

## proposal 1

lose core

fm/
├── config/                # App-wide configuration
│   ├── config.py
│   └── paths.py
├── data/                 # Database and data handling
├── gui/                  # UI components
├── modules/              # Feature modules
├── services/            # Application services
│   ├── event_bus.py
│   ├── logger.py
│   └── utils/          # Shared utilities
│       └── date_utils.py
└── standards/           # Shared standards
    └── fm_standard_columns.py

should possibly call "modules" 'feature_modules'
