_ai_prompt.md

Architectural Refactoring for Sidebar Navigation:

relevant files:
[@nav_pane](/flatmate/src/fm/gui/components/nav_pane.py)
[@nav_buttons](/flatmate/src/fm/gui/components/nav_buttons.py)
[@main_window](/flatmate/src/fm/gui/main_window.py)
[@module_co-ordinator](/flatmate/src/fm/modules/_module_co-ordinator)
[@event_bus](/flatmate/src/fm/core/event_bus.py)
Context:
- Current NavPane module transition system uses imports and direct method calls for module transitions
- We want to implement event-based transitions to reduce coupling
- Follows clean architectural principles of separation of concerns
- Leverage existing icon management system (IconManager, IconRenderer, IconService)?
- Leverage existing event system ? Or dedicated service?
(log leverages event bus I believe - pretty sure event bus can have diffrent channels, why duplicate code?)


Requirements:
1. Create an event-based navigation system for sidebar buttons
2. Decouple NavPane from direct module knowledge
3. Maintain existing button styling and layout
4. Use signals or an event service for module transitions
5. Ensure compatibility with current MainWindow and module structure

Specific Constraints:
- Avoid modifying existing icon management system
- Preserve current button rendering and styling
- Minimal changes to existing component interfaces
- Support dynamic module registration

Proposed High-Level Approach:
- Implement a NavigationEventService / channel in event_bus
- Use Qt signals for loose coupling - or pub sub pattern? convert ? dicuss pros and cons
- Create abstract base classes for sidebar components ensures concistency - side_bar base classes buttons, panes, manager

Discuss and refine,
create an action plan.


