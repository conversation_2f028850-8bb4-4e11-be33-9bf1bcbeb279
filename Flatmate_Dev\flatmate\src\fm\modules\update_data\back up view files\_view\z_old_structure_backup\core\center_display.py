"""Center display widget for Update Data module."""

import pandas as pd
from PySide6.QtCore import Signal
from PySide6.QtWidgets import QVBoxLayout, QWidget

from ..panels.panel_data_display import DataDisplayPanel
from ..panels.panel_file_display import FileDisplayPanel
# These will be updated once we move the panel files
from ..panels.panel_welcome import WelcomePanel
from .composite_panel import CompositePanel


class CenterDisplay(QWidget):
    """Main center display widget for Update Data module.
    
    This class uses the Composite pattern to manage different panels
    that can be shown in the center area of the Update Data module.
    """
    
    # Forward signals from internal components
    publish_file_removed = Signal(str)  # Publishes path of removed file
    publish_file_selected = Signal(str)  # Publishes path of selected file
    
    # Panel identifiers
    WELCOME_PANEL = "welcome"
    FILE_PANEL = "file"
    DATA_PANEL = "data"
    
    def __init__(self, parent=None):
        """Initialize the center display widget."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Create composite panel to manage different views
        self.panel_container = CompositePanel(self)
        self.main_layout.addWidget(self.panel_container)
        
        # Create panels
        self._create_panels()
        
        # Show welcome panel by default
        self.panel_container.show_component(self.WELCOME_PANEL)
    
    def _create_panels(self):
        """Create all panel components."""
        # Welcome panel
        welcome_panel = WelcomePanel()
        self.panel_container.add_component(self.WELCOME_PANEL, welcome_panel)
        
        # File display panel
        file_panel = FileDisplayPanel()
        file_panel.publish_file_removed.connect(self.publish_file_removed)
        file_panel.publish_file_selected.connect(self.publish_file_selected)
        self.panel_container.add_component(self.FILE_PANEL, file_panel)
        
        # Data display panel
        data_panel = DataDisplayPanel()
        self.panel_container.add_component(self.DATA_PANEL, data_panel)
    
    def _connect_signals(self):
        """Connect internal signals."""
        # All signals are now connected in _create_panels()
    
    def set_source_path(self, path: str):
        """Set the source folder path."""
        # Get the file panel component
        file_panel = self.panel_container.get_component(self.FILE_PANEL)
        if file_panel:
            file_panel.set_source_path(path)
    
    def set_save_path(self, path: str):
        """Set the save location path."""
        # Get the file panel component
        file_panel = self.panel_container.get_component(self.FILE_PANEL)
        if file_panel:
            file_panel.set_save_path(path)
    
    def set_files(self, files: list, source_dir: str = ""):
        """Set the files to display in the tree.
        
        Args:
            files: List of file paths
            source_dir: Source directory for relative paths
        """
        # Get the file panel component
        file_panel = self.panel_container.get_component(self.FILE_PANEL)
        if file_panel:
            file_panel.set_files(files, source_dir)
            # Show the file panel
            self.panel_container.show_component(self.FILE_PANEL)
    
    def get_files(self) -> list[str]:
        """Get all file paths in the tree."""
        # Get the file panel component
        file_panel = self.panel_container.get_component(self.FILE_PANEL)
        if file_panel:
            return file_panel.get_files()
        return []

    def display_master_csv(self, df: pd.DataFrame, file_info: str = ""):
        """Display a DataFrame in a table.
        
        Args:
            df: DataFrame to display
            file_info: Information about the file being displayed
        """
        # Get the data panel component
        data_panel = self.panel_container.get_component(self.DATA_PANEL)
        if data_panel:
            data_panel.display_dataframe(df, file_info)
            # Show the data panel
            self.panel_container.show_component(self.DATA_PANEL)
    
    def display_welcome(self):
        """Display welcome message."""
        # Show the welcome panel
        self.panel_container.show_component(self.WELCOME_PANEL)
        
        # Clear file panel if it exists
        file_panel = self.panel_container.get_component(self.FILE_PANEL)
        if file_panel:
            file_panel.clear()

    def show_error(self, message: str):
        """Show error message."""
        # Get the current visible component and show error
        current_component = self.panel_container.get_current_component()
        if current_component is not None:
            current_component.show_error(message)
    
    def show_success(self, message: str):
        """Show success message."""
        # Get the current visible component and show success
        current_component = self.panel_container.get_current_component()
        if current_component is not None:
            current_component.show_success(message)
