"""
Simple State Coordinator for Update Data GUI.

Centralized state management following KISS principle.
Replaces the complex StateEngine with simple, testable state logic.
"""

from pathlib import Path
from typing import Optional


class SimpleStateCoordinator:
    """
    Centralized state management for Update Data GUI.
    Encapsulates all UI state transitions in a single, testable location.
    """
    
    def __init__(self, view, guide_pane):
        self.view = view
        self.guide_pane = guide_pane
        self.state = {
            'source_configured': False,
            'destination_configured': False,
            'processing': False,
            'source_type': None,  # 'folder' or 'files'
            'source_path': None,
            'destination_path': None,
            'file_count': 0,
            'supported_types': ['.csv', '.pdf', '.ofx']
        }
    
    def set_source_folder(self, path: str, file_count: int):
        """Handle folder selection."""
        self.state.update({
            'source_configured': True,
            'source_type': 'folder',
            'source_path': path,
            'file_count': file_count
        })
        self._update_ui_state()
        self.guide_pane.display(f"Found {file_count} files ready for processing")
    
    def set_source_files(self, files: list[str]):
        """Handle file selection."""
        self.state.update({
            'source_configured': True,
            'source_type': 'files',
            'source_path': files,
            'file_count': len(files)
        })
        self._update_ui_state()
        self.guide_pane.display(f"Selected {len(files)} files for processing")
    
    def set_destination_same_as_source(self):
        """Handle same-as-source destination."""
        self.state.update({
            'destination_configured': True,
            'destination_path': 'same_as_source'
        })
        self._update_ui_state()
        self.guide_pane.display("Files will be archived in source folder")
    
    def set_destination_custom(self, path: str):
        """Handle custom destination."""
        self.state.update({
            'destination_configured': True,
            'destination_path': path
        })
        self._update_ui_state()
        self.guide_pane.display(f"Files will be archived in {Path(path).name}")
    
    def is_ready_to_process(self) -> bool:
        """Check if all requirements are met."""
        return (self.state['source_configured'] and 
                self.state['destination_configured'] and 
                not self.state['processing'])
    
    def start_processing(self):
        """Handle processing start."""
        self.state['processing'] = True
        self.view.set_process_text("Processing...")
        self.view.set_all_controls_enabled(False)
        self.guide_pane.display("Processing files...")
    
    def complete_processing(self, success_count: int):
        """Handle processing completion."""
        self.state['processing'] = False
        self.view.set_process_text("View Results")
        self.view.set_all_controls_enabled(True)
        self.guide_pane.display(f"Successfully processed {success_count} files")
    
    def reset_to_initial(self):
        """Reset to initial state."""
        self.state = {
            'source_configured': False,
            'destination_configured': False,
            'processing': False,
            'source_type': None,
            'source_path': None,
            'destination_path': None,
            'file_count': 0,
            'supported_types': ['.csv', '.pdf', '.ofx']
        }
        self._update_ui_state()
        self.guide_pane.display("Select source files to begin")
    
    def _update_ui_state(self):
        """Central state transition logic."""
        ready_to_process = self.is_ready_to_process()
        
        # Update view states
        self.view.set_process_enabled(ready_to_process)
        self.view.set_archive_enabled(self.state['source_configured'])
        
        # Update guide pane context
        if ready_to_process:
            self.guide_pane.display(f"Ready to process {self.state['file_count']} files")
