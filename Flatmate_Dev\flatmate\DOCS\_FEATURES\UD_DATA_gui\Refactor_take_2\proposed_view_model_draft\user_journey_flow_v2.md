# Update Data GUI - User Journey Flow v2 (PRD Draft)

## Overview
This document describes the user experience for updating financial data through the Update Data GUI, incorporating guide pane feedback and active/inactive state management.

## User Experience Flow

### 🏠 **Welcome Screen State**
**Initial display:**
- **Source Files...** section (active)
- **Archive** section (inactive)
- **PROCESS FILES** button (inactive state)
- **guide_pane** info widget (ready to provide context)

### 🎯 **Step 1: Source Selection**

#### **Option A: Folder Selection**
**User action:** Click "Select Folder" under Source Files
**System response:**
- File dialog opens for folder selection
- **guide_pane** displays: "Found [X] CSV files ready for processing"
- **File display section** shows all recognized file types (.csv) to be implemented: pdf, ofx
- **Archive** section becomes active

#### **Option B: File Selection**  
**User action:** Click "Select Files" under Source Files
**System response:**
- File dialog opens for multi-file selection
- **guide_pane** displays: "Selected [X] files for processing"
- **File display section** shows selected files with details
- **Archive** section becomes active

### 📁 **Step 2: Archive Location**

#### **Option A: Same as Source**
**User action:** Select "Same as source" under Archive
**System response:**
- **guide_pane** displays: "Files will be moved to 'Archive' subfolder in source location"
- No additional folder selection required

#### **Option B: Custom Location**
**User action:** Select "Select folder..." under Archive
**System response:**
- Folder dialog opens for destination selection
- **guide_pane** displays: "Choose where to create 'Archive' folder for processed files"

### ⚡ **Step 3: Ready State**
**When both selections complete:**
- **PROCESS FILES** button transitions from inactive to active state
- **guide_pane** displays: "Ready to process [X] files"
- Checkbox appears: "Monitor this folder for new files [ ]"

### 🔄 **Processing State**
**User action:** Click active **PROCESS FILES** button
**System response:**
- **guide_pane** displays real-time progress: "Processing file 3 of 15..."
- Progress bar shows completion percentage
- **Cancel** button remains available
- **PROCESS FILES** button shows processing animation

### ✅ **Success State**
**Completion response:**
- **PROCESS FILES** button changes to **View Results** (active state)
- **guide_pane** displays: "Successfully processed [X] files"
- Results summary appears in main display area

## State Management

### Visual States
- **Inactive**: Grayed out, disabled interaction
- **Active**: Standard appearance, enabled interaction  
- **Processing**: Animated state with progress indication

### Guide Pane Behavior
- **Contextual**: Always relevant to current user action
- **Progressive**: Updates as user makes choices
- **Concise**: One clear message at a time

## Error Handling

### Invalid Source
**guide_pane**: "No compatible files found in selected location"
**System response**: Returns to source selection

### Processing Errors  
**guide_pane**: "Error processing [filename]: [specific error]"
**System response**: Continues with remaining files, provides error log

## User Paths Summary

### **Quick Path** (60% of users)
1. Select folder → Same as source → Process → View results

### **Organized Path** (30% of users)  
1. Select folder → Custom destination → Process → View results

### **Selective Path** (10% of users)
1. Select specific files → Custom destination → Process → View results

## Technical Implementation Notes

### State Coordination
- Central `StateCoordinator` class manages UI state transitions
- Simple boolean logic: `source_configured AND destination_configured`
- Guide pane updates via direct method calls

### File Display
- Real-time file discovery and validation
- Visual indicators for file types and counts
- Responsive to source changes

### Monitoring Option
- Checkbox state persists across sessions
- Background file monitoring when enabled
-possible implementation of option for automatic processing of new files (future enhancement)
