"""
Test GUI for Schema-Driven Update Data UI

This test GUI demonstrates the MVP state schema implementation with:
- SelectGroupWidget components
- CSV-driven state engine
- Schema-compliant state transitions
- Proper widget behavior as defined in the Excel schema

Run this file to test the schema implementation before integrating with main module.
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QTextEdit, QPushButton, QFileDialog, QMessageBox
)
from PySide6.QtCore import Qt, QTimer

# Add the project root to Python path for imports
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from fm.gui._shared_components.widgets import (
    HeadingLabel, SubheadingLabel, InfoLabel,
    ActionButton, ExitButton, LabeledCheckBox
)
from select_group_widget import (
    SelectGroupWidget, SelectGroupConfig,
    create_source_group_widget, create_save_group_widget
)
from state_engine import StateEngine, UIState


class SchemaTestGUI(QMainWindow):
    """
    Test GUI demonstrating schema-driven UI behavior.
    
    This GUI implements the exact components and states defined in the
    MVP state schema Excel file, providing a testbed for validating
    the schema accuracy and state engine functionality.
    """
    
    def __init__(self):
        super().__init__()
        self.state_engine = None
        self.selected_folder = None
        self.detected_files = []
        
        self._init_ui()
        self._setup_state_engine()
        self._connect_signals()
        
        # Start in initial state
        self.log_message("GUI initialized in INITIAL state")
    
    def _init_ui(self):
        """Initialize the UI components according to schema."""
        self.setWindowTitle("Update Data UI - Schema Test")
        self.setGeometry(100, 100, 1000, 700)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Left panel (schema components)
        left_panel = self._create_left_panel()
        main_layout.addWidget(left_panel, 1)
        
        # Right panel (test controls and log)
        right_panel = self._create_right_panel()
        main_layout.addWidget(right_panel, 1)
    
    def _create_left_panel(self):
        """Create left panel with schema-compliant components."""
        panel = QWidget()
        panel.setMaximumWidth(300)
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Title (schema: title -> HeadingLabel)
        self.title = HeadingLabel("Update Data")
        layout.addWidget(self.title)
        
        # Source Group (schema: source_group -> SelectGroupWidget)
        self.source_group = create_source_group_widget()
        layout.addWidget(self.source_group)
        
        # Save Group (schema: save_group -> SelectGroupWidget) 
        self.save_group = create_save_group_widget()
        layout.addWidget(self.save_group)
        
        # Process Label (schema: process_label -> SubheadingLabel)
        self.process_label = SubheadingLabel("3. Process")
        layout.addWidget(self.process_label)
        
        # Database Update Checkbox (schema: db_update_checkbox -> LabeledCheckBox)
        self.db_update_checkbox = LabeledCheckBox(
            label_text="Update Database",
            checked=True,
            tooltip="Store processed transactions in the central database"
        )
        layout.addWidget(self.db_update_checkbox)
        
        # Process Button (schema: process_btn -> ActionButton)
        self.process_btn = ActionButton("Process Files")
        layout.addWidget(self.process_btn)
        
        # Monitoring Status (schema: monitoring_status -> InfoLabel)
        self.monitoring_status = InfoLabel("Ready to select source folder")
        layout.addWidget(self.monitoring_status)
        
        # Add spacer
        layout.addStretch()
        
        # Cancel Button (schema: cancel_btn -> ExitButton)
        self.cancel_btn = ExitButton("Cancel")
        layout.addWidget(self.cancel_btn)
        
        return panel
    
    def _create_right_panel(self):
        """Create right panel with test controls and logging."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Test controls section
        controls_label = HeadingLabel("Test Controls")
        layout.addWidget(controls_label)
        
        # State control buttons
        state_layout = QHBoxLayout()
        
        self.btn_initial = QPushButton("INITIAL")
        self.btn_folder_selected = QPushButton("FOLDER_SELECTED")
        self.btn_monitoring = QPushButton("MONITORING")
        self.btn_files_detected = QPushButton("FILES_DETECTED")
        
        state_layout.addWidget(self.btn_initial)
        state_layout.addWidget(self.btn_folder_selected)
        state_layout.addWidget(self.btn_monitoring)
        state_layout.addWidget(self.btn_files_detected)
        
        layout.addLayout(state_layout)
        
        # File simulation controls
        sim_layout = QHBoxLayout()
        
        self.btn_simulate_files = QPushButton("Simulate Files Detected")
        self.btn_clear_files = QPushButton("Clear Files")
        
        sim_layout.addWidget(self.btn_simulate_files)
        sim_layout.addWidget(self.btn_clear_files)
        
        layout.addLayout(sim_layout)
        
        # Current state display
        self.state_display = InfoLabel("Current State: INITIAL")
        layout.addWidget(self.state_display)
        
        # Log section
        log_label = SubheadingLabel("Event Log")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(300)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # Schema info section
        schema_label = SubheadingLabel("Schema Component Status")
        layout.addWidget(schema_label)
        
        self.schema_status = QTextEdit()
        self.schema_status.setMaximumHeight(200)
        self.schema_status.setReadOnly(True)
        layout.addWidget(self.schema_status)
        
        return panel
    
    def _setup_state_engine(self):
        """Setup the CSV state engine."""
        try:
            # Path to the CSV schema - adjust for correct project structure
            schema_path = Path(__file__).parent.parent.parent.parent.parent.parent / "DOCS" / "_REFACTORING_workspace" / "UI_REFACTOR_UPDATE_DATA" / "Schema" / "mvp_state_schema.csv"
            
            if not schema_path.exists():
                self.log_message(f"WARNING: Schema CSV not found at {schema_path}")
                self.log_message("Creating mock state engine for testing")
                self._create_mock_state_engine()
                return
            
            self.state_engine = StateEngine(str(schema_path))
            
            # Register components with state engine
            self.state_engine.register_component('title', self.title)
            self.state_engine.register_component('source_group', self.source_group)
            self.state_engine.register_component('save_group', self.save_group)
            self.state_engine.register_component('process_label', self.process_label)
            self.state_engine.register_component('db_update_checkbox', self.db_update_checkbox)
            self.state_engine.register_component('process_btn', self.process_btn)
            self.state_engine.register_component('monitoring_status', self.monitoring_status)
            self.state_engine.register_component('cancel_btn', self.cancel_btn)
            
            self.log_message("State engine initialized successfully")
            
        except Exception as e:
            self.log_message(f"ERROR: Failed to setup state engine: {e}")
            self._create_mock_state_engine()
    
    def _create_mock_state_engine(self):
        """Create a mock state engine for testing when CSV is not available."""
        self.log_message("Using mock state engine for testing")
        # Apply initial state manually
        self.save_group.set_visible(False)
        self.process_btn.setEnabled(False)
        self.monitoring_status.setText("Ready to select source folder")
    
    def _connect_signals(self):
        """Connect all signals for testing."""
        # Schema component signals
        self.source_group.button_clicked.connect(self._on_source_select_clicked)
        self.source_group.option_changed.connect(self._on_source_option_changed)
        
        self.save_group.button_clicked.connect(self._on_save_select_clicked)
        self.save_group.option_changed.connect(self._on_save_option_changed)
        
        self.process_btn.clicked.connect(self._on_process_clicked)
        self.cancel_btn.clicked.connect(self._on_cancel_clicked)
        
        self.db_update_checkbox.state_changed.connect(self._on_db_update_changed)
        
        # Test control signals
        self.btn_initial.clicked.connect(lambda: self._force_state(UIState.INITIAL))
        self.btn_folder_selected.clicked.connect(lambda: self._force_state(UIState.FOLDER_SELECTED))
        self.btn_monitoring.clicked.connect(lambda: self._force_state(UIState.MONITORING))
        self.btn_files_detected.clicked.connect(lambda: self._force_state(UIState.FILES_DETECTED))
        
        self.btn_simulate_files.clicked.connect(self._simulate_files_detected)
        self.btn_clear_files.clicked.connect(self._clear_files)
        
        # State engine signals (if available)
        if self.state_engine:
            self.state_engine.state_changed.connect(self._on_state_changed)
            self.state_engine.component_updated.connect(self._on_component_updated)
    
    # === EVENT HANDLERS ===
    
    def _on_source_select_clicked(self):
        """Handle source select button click."""
        self.log_message("Source select button clicked")
        
        folder = QFileDialog.getExistingDirectory(self, "Select Source Folder")
        if folder:
            self.selected_folder = folder
            self.log_message(f"Selected folder: {folder}")
            
            # Trigger state transition
            if self.state_engine:
                self.state_engine.trigger_transition('folder_selected', folder_path=folder)
            else:
                self._mock_folder_selected(folder)
    
    def _on_source_option_changed(self, option: str):
        """Handle source option change."""
        self.log_message(f"Source option changed to: {option}")
    
    def _on_save_select_clicked(self):
        """Handle save select button click."""
        self.log_message("Save select button clicked")
        
        folder = QFileDialog.getExistingDirectory(self, "Select Save Location")
        if folder:
            self.log_message(f"Selected save location: {folder}")
    
    def _on_save_option_changed(self, option: str):
        """Handle save option change."""
        self.log_message(f"Save option changed to: {option}")
    
    def _on_process_clicked(self):
        """Handle process button click."""
        self.log_message("Process button clicked - starting file processing")
        
        if self.state_engine:
            self.state_engine.trigger_transition('process_clicked')
        else:
            self._mock_processing()
    
    def _on_cancel_clicked(self):
        """Handle cancel button click."""
        self.log_message("Cancel button clicked")
        self.close()
    
    def _on_db_update_changed(self, checked: bool):
        """Handle database update checkbox change."""
        self.log_message(f"Database update checkbox: {'checked' if checked else 'unchecked'}")
    
    def _on_state_changed(self, from_state: str, to_state: str):
        """Handle state engine state changes."""
        self.log_message(f"State changed: {from_state} -> {to_state}")
        self.state_display.setText(f"Current State: {to_state.upper()}")
        self._update_schema_status()
    
    def _on_component_updated(self, component_id: str, state_config: dict):
        """Handle component state updates."""
        self.log_message(f"Component '{component_id}' updated: {state_config}")
    
    # === TEST HELPERS ===
    
    def _force_state(self, state: UIState):
        """Force a state change for testing."""
        self.log_message(f"Forcing state change to: {state.value}")
        
        if self.state_engine:
            self.state_engine.force_state_change(state)
        else:
            self._mock_state_change(state)
    
    def _simulate_files_detected(self):
        """Simulate files being detected in the monitored folder."""
        self.detected_files = [
            "test_file_1.csv",
            "test_file_2.csv", 
            "test_file_3.csv"
        ]
        
        self.log_message(f"Simulated {len(self.detected_files)} files detected")
        
        if self.state_engine:
            self.state_engine.trigger_transition('files_detected', files=self.detected_files)
        else:
            self._mock_files_detected()
    
    def _clear_files(self):
        """Clear detected files."""
        self.detected_files = []
        self.log_message("Cleared detected files")
        self.process_btn.setEnabled(False)
    
    def _mock_folder_selected(self, folder: str):
        """Mock folder selected behavior when state engine not available."""
        self.save_group.set_visible(True)
        self.monitoring_status.setText(f"Monitoring: {os.path.basename(folder)}")
        self.log_message("Mock: Save options now visible, monitoring started")
    
    def _mock_files_detected(self):
        """Mock files detected behavior."""
        self.process_btn.setEnabled(True)
        self.monitoring_status.setText(f"Files ready: {len(self.detected_files)} files detected")
        self.log_message("Mock: Process button enabled, files ready")
    
    def _mock_processing(self):
        """Mock processing behavior."""
        self.process_btn.setEnabled(False)
        self.monitoring_status.setText("Processing files...")
        
        # Simulate processing completion after 2 seconds
        QTimer.singleShot(2000, self._mock_processing_complete)
    
    def _mock_processing_complete(self):
        """Mock processing completion."""
        self.process_btn.setEnabled(True)
        self.monitoring_status.setText("Processing complete - ready for next batch")
        self.log_message("Mock: Processing completed successfully")
    
    def _mock_state_change(self, state: UIState):
        """Mock state change behavior."""
        self.state_display.setText(f"Current State: {state.value.upper()}")
        
        if state == UIState.INITIAL:
            self.save_group.set_visible(False)
            self.process_btn.setEnabled(False)
            self.monitoring_status.setText("Ready to select source folder")
        elif state == UIState.FOLDER_SELECTED:
            self.save_group.set_visible(True)
            self.monitoring_status.setText("Folder selected - monitoring active")
        elif state == UIState.FILES_DETECTED:
            self.process_btn.setEnabled(True)
            self.monitoring_status.setText("Files detected - ready to process")
    
    def _update_schema_status(self):
        """Update schema component status display."""
        status_text = "Schema Component Status:\n\n"
        
        components = [
            ('title', self.title),
            ('source_group', self.source_group),
            ('save_group', self.save_group),
            ('process_btn', self.process_btn),
            ('monitoring_status', self.monitoring_status)
        ]
        
        for comp_id, widget in components:
            visible = widget.isVisible()
            enabled = widget.isEnabled()
            status_text += f"{comp_id}: visible={visible}, enabled={enabled}\n"
        
        self.schema_status.setText(status_text)
    
    def log_message(self, message: str):
        """Add message to event log."""
        self.log_text.append(f"[{QTimer().remainingTime()}] {message}")
        
        # Auto-scroll to bottom
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())


def main():
    """Run the schema test GUI."""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Update Data Schema Test")
    app.setApplicationVersion("1.0")
    
    # Create and show the test GUI
    window = SchemaTestGUI()
    window.show()
    
    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
