"""
UpdateData StatusBar widget for displaying status messages in the Update Data module.
Uses Qt's QStatusBar for a standard status display.
"""

from fm.core.services.event_bus import Events, global_event_bus
from PySide6.QtCore import Qt
from PySide6.QtWidgets import <PERSON><PERSON>abel, QProgressBar, QStatusBar


class UpdateDataStatusBar(QStatusBar):
    """StatusBar widget specialized for the Update Data module."""

    def __init__(self, parent=None):
        """Initialize the Update Data status bar widget."""
        super().__init__(parent)
        self._customize_appearance()
        self._add_progress_bar()

        # Subscribe to events
        self.event_bus = global_event_bus
        self.event_bus.subscribe(Events.INFO_MESSAGE, self._handle_info_message)
        self.event_bus.subscribe(Events.INFO_CLEAR, self._handle_info_clear)

    def _customize_appearance(self):
        """Customize the appearance to make it more discrete."""
        # Make the background slightly lighter than the app background
        # Assuming dark theme with background close to #222
        self.setStyleSheet(
            """
            QStatusBar {
                background-color: #2a2a2a; 
                border-top: 1px solid #333;
                color: #aaa;
                font-style: italic;
            }
            QStatusBar::item {
                border: none;
            }
        """
        )

        # Reduce the height
        self.setFixedHeight(24)

    def _add_progress_bar(self):
        """Add a progress bar to the status bar."""
        # Create progress bar (hidden by default)
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setFixedWidth(150)
        self.progress_bar.setFixedHeight(16)
        self.progress_bar.setVisible(False)

        # Add progress bar as permanent widget (right-aligned)
        self.addPermanentWidget(self.progress_bar)

    def set_status(self, message, is_error=False):
        """Set the status message to display.

        Args:
            message: The message text to display
            is_error: Whether this is an error message
        """
        if not message:
            self.clearMessage()
            return

        # Apply error styling if needed
        if is_error:
            self.setStyleSheet(
                """
                QStatusBar {
                    background-color: #2a2a2a; 
                    border-top: 1px solid #333;
                    color: #d32f2f;
                    font-weight: bold;
                }
                QStatusBar::item {
                    border: none;
                }
            """
            )
        else:
            self.setStyleSheet(
                """
                QStatusBar {
                    background-color: #2a2a2a; 
                    border-top: 1px solid #333;
                    color: #aaa;
                    font-style: italic;
                }
                QStatusBar::item {
                    border: none;
                }
            """
            )

        # Set the message text
        self.showMessage(message)

    def set_progress(self, current, total):
        """Show progress information.

        Args:
            current: Current progress value
            total: Total progress value
        """
        if total <= 0:
            self.progress_bar.setVisible(False)
            return

        # Calculate percentage
        percentage = min(100, int((current / total) * 100))

        # Update progress bar
        self.progress_bar.setValue(percentage)
        self.progress_bar.setVisible(True)

    def set_error(self, message):
        """Display an error message.

        Args:
            message: The error message to display
        """
        self.set_status(message, is_error=True)

    def clear(self):
        """Clear all displays."""
        # Clear the message
        self.clearMessage()

        # Also hide the progress bar
        self.progress_bar.setVisible(False)

        # Reset styling
        self.setStyleSheet(
            """
            QStatusBar {
                background-color: #2a2a2a; 
                border-top: 1px solid #333;
                color: #aaa;
                font-style: italic;
            }
            QStatusBar::item {
                border: none;
            }
        """
        )

    # Event handlers
    def _handle_info_message(self, message):
        """Handle info messages from the event bus."""
        if isinstance(message, dict):
            # Handle progress updates
            if "progress" in message and "total" in message:
                self.set_progress(message["progress"], message["total"])
                if "text" in message:
                    self.set_status(
                        message["text"], is_error=message.get("is_error", False)
                    )
            else:
                self.set_status(
                    message.get("text", ""), is_error=message.get("is_error", False)
                )
        else:
            # Simple text message
            self.set_status(message)

    def _handle_info_clear(self, _=None):
        """Handle clear events from the event bus."""
        self.clear()
