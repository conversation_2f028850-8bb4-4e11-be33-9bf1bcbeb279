"""
View components for the Update Data module.

This package provides a modular, component-based UI for the Update Data module.
Components are organized into the following categories:

- _common: Base components and core structural elements
- center_panel: Center panel components for main content display
- left_panel: Left panel components for navigation
- right_panel: Right panel components for settings and options
- utils: Helper utilities for the view components
"""

# Base components imported directly where needed to avoid circular imports
# from fm.gui._shared_components.base.base_pane import BasePane
# from fm.gui._shared_components import BasePanelComponent
# Import CenterPanelManager directly from center_panel.py module (not the directory)
# This avoids the circular import through center_panel/__init__.py
import importlib.util
import sys
from pathlib import Path

# Get the path to center_panel.py
center_panel_path = Path(__file__).parent / "center_panel.py"
spec = importlib.util.spec_from_file_location("center_panel", center_panel_path)
center_panel_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(center_panel_module)
CenterPanelManager = center_panel_module.CenterPanelManager
# TODO: Temporarily commented out to avoid circular imports
# Import components from center_panel/ package
# from .center_panel import PanelSwitcher, DataPane, FilePane, WelcomePane
# Import left panel components
from .left_panel_manager import LeftPanelManager
# Import right panel components
from .right_panel_components import RightPanelManager  # From right_panel.py
from .right_panel_components.options_pane import OptionsPane
# Import utilities
from .utils.file_helper import FileHelper

__all__ = [
    # Panel components
    # 'WelcomePane',  # TODO: Temporarily commented out
    # 'FilePane',     # TODO: Temporarily commented out
    # 'DataPane',     # TODO: Temporarily commented out
    'CenterPanelManager',
    # 'PanelSwitcher',  # TODO: Temporarily commented out
    
    # Left panel components
    'LeftPanelManager',
    
    # Right panel components
    'RightPanelManager',
    'OptionsPane',
    
    # Utilities
    'FileHelper',
]
