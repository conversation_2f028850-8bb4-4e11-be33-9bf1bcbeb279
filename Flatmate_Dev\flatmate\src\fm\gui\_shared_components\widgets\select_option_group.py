from typing import List, Optional
from PySide6.QtCore import Signal
from PySide6.QtWidgets import (QComboBox, QLabel, QPushButton, QVBoxLayout,
                               QWidget)

#todo: this is designed to be an easily re-usable base class, for a label, option menu and select button
# this and widgets like it should be available in the core gui module
#it may be most suitable for left and right panels as currently configured.


class SelectOptionGroup(QWidget):
    """
    A reusable widget for selecting options from a dropdown with a select button.
    Can be used as a base class for any option selection UI component.
    """
    
    option_changed = Signal(str)  # Emitted when the selected option changes
    select_clicked = Signal(str)  # Emitted when the select button is clicked with current option
    
    def __init__(self, 
                 options: List[str], 
                 label_text: str = "Options", 
                 button_text: str = "Select...",
                 parent: Optional[QWidget] = None):
        """
        Initialize a new SelectOptionGroup.
        
        Args:
            options: List of string options to display in the dropdown
            label_text: Text to display above the dropdown
            button_text: Text to display on the select button
            parent: Parent widget
        """
        super().__init__(parent)
        self.options = options
        self.label_text = label_text
        self.button_text = button_text
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Label
        self.label = QLabel(self.label_text)
        self.label.setObjectName("subheading")
        layout.addWidget(self.label)
        
        # Combo box
        self.combo = QComboBox()
        self.combo.addItems(self.options)
        self.combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        layout.addWidget(self.combo)
        
        # Select button
        self.select_btn = QPushButton(self.button_text)
        self.select_btn.setProperty("type", "select_btn")
        layout.addWidget(self.select_btn)
        
    def _connect_signals(self):
        """Connect widget signals to slots."""
        self.combo.currentTextChanged.connect(self.option_changed.emit)
        self.select_btn.clicked.connect(self._on_select_clicked)
        
    def _on_select_clicked(self):
        """Handle select button click."""
        current_option = self.combo.currentText()
        self.select_clicked.emit(current_option)
    
    def get_current_option(self) -> str:
        """Get the currently selected option."""
        return self.combo.currentText()
    
    def set_options(self, options: List[str]):
        """Update the available options."""
        current = self.combo.currentText()
        self.combo.clear()
        self.combo.addItems(options)
        
        # Try to restore the previous selection if it exists in the new options
        index = self.combo.findText(current)
        if index >= 0:
            self.combo.setCurrentIndex(index)
    
    def set_current_option(self, option: str):
        """Set the current option if it exists in the list."""
        index = self.combo.findText(option)
        if index >= 0:
            self.combo.setCurrentIndex(index)