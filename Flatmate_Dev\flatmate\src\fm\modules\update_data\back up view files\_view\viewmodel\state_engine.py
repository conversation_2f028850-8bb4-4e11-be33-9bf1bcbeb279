"""
CSV State Engine for Update Data UI

Reads the MVP state schema CSV and drives UI state transitions as defined in the
planning documents. This engine provides a data-driven approach to UI state management
without requiring code changes for state behavior modifications.
"""

import csv
import os
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from PySide6.QtCore import QObject, Signal


class UIState(Enum):
    """UI states as defined in the schema."""
    INITIAL = "initial"
    FOLDER_SELECTED = "folder_selected"
    MONITORING = "monitoring"
    FILES_DETECTED = "files_detected"
    PROCESSING = "processing"
    COMPLETE = "complete"
    ERROR = "error"


@dataclass
class ComponentState:
    """Represents the state configuration for a UI component."""
    component_id: str
    widget_type: str
    base_class: str
    state_initial: str
    text_initial: str
    trigger: str
    response: str
    text_folder_selected: str
    current_variable: str
    notes: str
    priority: str


@dataclass
class StateTransition:
    """Represents a state transition rule."""
    from_state: str
    to_state: str
    trigger: str
    conditions: str
    actions: str
    affected_components: str
    validation: str
    notes: str


class StateEngine(QObject):
    """
    CSV-driven state engine for Update Data UI.
    
    Reads state definitions from CSV schema and manages UI component states
    based on triggers and transitions defined in the data.
    
    Signals:
        state_changed: Emitted when UI state changes
        component_updated: Emitted when a component state is updated
        validation_failed: Emitted when state validation fails
    """
    
    state_changed = Signal(str, str)  # from_state, to_state
    component_updated = Signal(str, dict)  # component_id, state_config
    validation_failed = Signal(str, str)  # component_id, error_message
    
    def __init__(self, schema_csv_path: str, parent=None):
        """
        Initialize state engine with CSV schema.
        
        Args:
            schema_csv_path: Path to the CSV schema file
            parent: Parent QObject
        """
        super().__init__(parent)
        self.schema_csv_path = schema_csv_path
        self.current_state = UIState.INITIAL
        self.components: Dict[str, ComponentState] = {}
        self.transitions: List[StateTransition] = []
        self.component_widgets: Dict[str, Any] = {}
        self.validation_functions: Dict[str, Callable] = {}
        
        self._load_schema()
        self._setup_default_validations()
    
    def _load_schema(self):
        """Load component states and transitions from CSV schema."""
        if not os.path.exists(self.schema_csv_path):
            raise FileNotFoundError(f"Schema CSV not found: {self.schema_csv_path}")
        
        with open(self.schema_csv_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                # Skip comment rows and empty rows
                if row.get('component_id', '').startswith('#') or not row.get('component_id'):
                    continue
                
                # Parse component state
                component = ComponentState(
                    component_id=row.get('component_id', ''),
                    widget_type=row.get('widget_type', ''),
                    base_class=row.get('base_class', ''),
                    state_initial=row.get('state_initial', ''),
                    text_initial=row.get('text_initial', ''),
                    trigger=row.get('trigger', ''),
                    response=row.get('response', ''),
                    text_folder_selected=row.get('text_folder_selected', ''),
                    current_variable=row.get('current_variable', ''),
                    notes=row.get('notes', ''),
                    priority=row.get('priority', '')
                )
                
                if component.component_id:
                    self.components[component.component_id] = component
    
    def _setup_default_validations(self):
        """Setup default validation functions."""
        self.validation_functions.update({
            'folder_exists': self._validate_folder_exists,
            'files_valid': self._validate_files,
            'process_ready': self._validate_process_ready
        })
    
    def register_component(self, component_id: str, widget: Any):
        """
        Register a UI component with the state engine.
        
        Args:
            component_id: Component identifier from schema
            widget: The actual widget instance
        """
        self.component_widgets[component_id] = widget
        
        # Apply initial state
        if component_id in self.components:
            self._apply_component_state(component_id, self.current_state)
    
    def trigger_transition(self, trigger: str, **kwargs):
        """
        Trigger a state transition based on trigger name.
        
        Args:
            trigger: Trigger name from schema
            **kwargs: Additional context data
        """
        # Find matching transitions
        matching_transitions = [
            t for t in self.transitions 
            if t.trigger == trigger and t.from_state == self.current_state.value
        ]
        
        if not matching_transitions:
            # Check for component-specific triggers
            self._handle_component_trigger(trigger, **kwargs)
            return
        
        # Execute first matching transition
        transition = matching_transitions[0]
        self._execute_transition(transition, **kwargs)
    
    def _handle_component_trigger(self, trigger: str, **kwargs):
        """Handle component-specific triggers that don't change global state."""
        for component_id, component in self.components.items():
            if component.trigger == trigger:
                self._apply_component_response(component_id, component.response, **kwargs)
    
    def _execute_transition(self, transition: StateTransition, **kwargs):
        """Execute a state transition."""
        # Validate conditions if specified
        if transition.conditions and not self._validate_conditions(transition.conditions, **kwargs):
            self.validation_failed.emit(transition.trigger, f"Conditions not met: {transition.conditions}")
            return
        
        # Change state
        old_state = self.current_state
        self.current_state = UIState(transition.to_state)
        
        # Execute actions
        if transition.actions:
            self._execute_actions(transition.actions, **kwargs)
        
        # Update affected components
        if transition.affected_components:
            self._update_affected_components(transition.affected_components)
        
        # Emit state change signal
        self.state_changed.emit(old_state.value, self.current_state.value)
    
    def _apply_component_state(self, component_id: str, state: UIState):
        """Apply state configuration to a component."""
        if component_id not in self.components or component_id not in self.component_widgets:
            return
        
        component = self.components[component_id]
        widget = self.component_widgets[component_id]
        
        # Determine state configuration
        state_config = self._get_component_state_config(component, state)
        
        # Apply configuration to widget
        self._apply_widget_config(widget, state_config)
        
        # Emit component update signal
        self.component_updated.emit(component_id, state_config)
    
    def _get_component_state_config(self, component: ComponentState, state: UIState) -> dict:
        """Get state configuration for a component based on current state."""
        config = {
            'visible': True,
            'enabled': True,
            'text': component.text_initial
        }
        
        # Apply state-specific configurations
        if state == UIState.INITIAL:
            if component.state_initial == 'hidden':
                config['visible'] = False
            elif component.state_initial == 'disabled':
                config['enabled'] = False
        elif state == UIState.FOLDER_SELECTED:
            if component.text_folder_selected:
                config['text'] = component.text_folder_selected
        
        return config
    
    def _apply_widget_config(self, widget: Any, config: dict):
        """Apply configuration to a widget."""
        try:
            # Handle visibility
            if 'visible' in config and hasattr(widget, 'setVisible'):
                widget.setVisible(config['visible'])
            
            # Handle enabled state
            if 'enabled' in config and hasattr(widget, 'setEnabled'):
                widget.setEnabled(config['enabled'])
            
            # Handle text updates
            if 'text' in config:
                if hasattr(widget, 'setText'):
                    widget.setText(config['text'])
                elif hasattr(widget, 'set_text'):
                    widget.set_text(config['text'])
            
            # Handle SelectGroupWidget specific methods
            if hasattr(widget, 'apply_state_config'):
                widget.apply_state_config(config)
                
        except Exception as e:
            print(f"Error applying widget config: {e}")
    
    def _apply_component_response(self, component_id: str, response: str, **kwargs):
        """Apply component response action."""
        if component_id not in self.component_widgets:
            return
        
        widget = self.component_widgets[component_id]
        
        # Handle common responses
        if response == 'update_display':
            if 'text' in kwargs and hasattr(widget, 'setText'):
                widget.setText(kwargs['text'])
        elif response == 'show_dropdown':
            if hasattr(widget, 'setVisible'):
                widget.setVisible(True)
        elif response == 'enable_button':
            if hasattr(widget, 'setEnabled'):
                widget.setEnabled(True)
    
    def _validate_conditions(self, conditions: str, **kwargs) -> bool:
        """Validate transition conditions."""
        # Simple condition validation - can be extended
        if conditions in self.validation_functions:
            return self.validation_functions[conditions](**kwargs)
        return True
    
    def _execute_actions(self, actions: str, **kwargs):
        """Execute transition actions."""
        # Parse and execute actions - simplified implementation
        action_list = [action.strip() for action in actions.split('+')]
        
        for action in action_list:
            if action == 'show_save_options':
                self._show_save_options()
            elif action == 'enable_monitoring':
                self._enable_monitoring()
            elif action == 'start_file_monitoring':
                self._start_file_monitoring(**kwargs)
    
    def _update_affected_components(self, affected_components: str):
        """Update components affected by state transition."""
        component_list = [comp.strip() for comp in affected_components.split(',')]
        
        for component_id in component_list:
            if component_id in self.components:
                self._apply_component_state(component_id, self.current_state)
    
    # === VALIDATION FUNCTIONS ===
    
    def _validate_folder_exists(self, folder_path: str = None, **kwargs) -> bool:
        """Validate that folder exists and is accessible."""
        if not folder_path:
            return False
        return os.path.exists(folder_path) and os.path.isdir(folder_path)
    
    def _validate_files(self, files: List[str] = None, **kwargs) -> bool:
        """Validate that files are valid for processing."""
        if not files:
            return False
        # Add file validation logic here
        return len(files) > 0
    
    def _validate_process_ready(self, **kwargs) -> bool:
        """Validate that all requirements are met for processing."""
        # Add comprehensive validation logic
        return True
    
    # === ACTION FUNCTIONS ===
    
    def _show_save_options(self):
        """Show save location options."""
        if 'save_menu' in self.component_widgets:
            self.component_widgets['save_menu'].setVisible(True)
        if 'save_select_btn' in self.component_widgets:
            self.component_widgets['save_select_btn'].setVisible(True)
    
    def _enable_monitoring(self):
        """Enable folder monitoring."""
        # Implement monitoring logic
        pass
    
    def _start_file_monitoring(self, **kwargs):
        """Start file monitoring process."""
        # Implement file monitoring logic
        pass
    
    # === PUBLIC API ===
    
    def get_current_state(self) -> UIState:
        """Get current UI state."""
        return self.current_state
    
    def get_component_state(self, component_id: str) -> dict:
        """Get current state of a component."""
        if component_id in self.component_widgets:
            widget = self.component_widgets[component_id]
            if hasattr(widget, 'get_state_info'):
                return widget.get_state_info()
        return {}
    
    def force_state_change(self, new_state: UIState):
        """Force a state change without validation."""
        old_state = self.current_state
        self.current_state = new_state
        
        # Update all components for new state
        for component_id in self.components:
            self._apply_component_state(component_id, new_state)
        
        self.state_changed.emit(old_state.value, new_state.value)
