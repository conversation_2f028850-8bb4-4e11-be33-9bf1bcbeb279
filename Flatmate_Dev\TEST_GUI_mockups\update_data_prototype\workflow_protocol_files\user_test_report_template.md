---
# User Test Report Template
*Last updated: 2025-07-23*
---
## Test Information
- **Date:** [YYYY-MM-DD]
- **State:** [Current State]

## Elements

### [Element Name]
- **Observed:** [What you saw]
- **Effect:** [Impact on user experience]
- **Goal:** [What should happen]
- **Suggestion:** [How to improve]

### [Element Name]
- **Observed:** [What you saw]
- **Effect:** [Impact on user experience]
- **Goal:** [What should happen]
- **Suggestion:** [How to improve]

(Add more elements as needed)

## Discussion Points
- [Key discussion points, questions, or observations]

## Actionable Plan
- [Specific actions to take based on this test]

---
*After each test:*
1. Fill out this template
2. Distill actionable items into `test_tasks.md`
3. Update workflow insights in `workflow_insights.md` (workflow process only)
4. Add UI implementation insights to `insights_gained.md`
