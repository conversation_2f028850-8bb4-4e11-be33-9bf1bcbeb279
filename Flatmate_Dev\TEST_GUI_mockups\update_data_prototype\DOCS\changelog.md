# Changelog

All notable changes to the Update Data Prototype will be documented in this file.

## [2025-07-23]

### Added
- Visual indicators for button states:
  - Process button now shows clear active/inactive states with color and font weight
  - Select button has distinct styling for enabled/disabled states
- Tooltips for folder paths to show full path on hover
- Additional guidance in info pane about setting default import folders

### Changed
- Renamed "Data Source" label to "Import data from:" for better clarity
- Updated "Source Folder" to "Import Folder" for consistency
- Improved folder path display with truncation for long paths
- Enhanced info pane text with more helpful guidance
- Updated folder selection dialog title to "Select Import Folder"

### Fixed
- Ambiguous button state visual feedback
- Unclear process button state
- Poor folder path display for long paths
- Vague UI terminology

## [Template for future entries]

## [YYYY-MM-DD]

### Added
- New features or capabilities

### Changed
- Modifications to existing functionality

### Fixed
- Bug fixes

### Removed
- Features or capabilities that were removed
