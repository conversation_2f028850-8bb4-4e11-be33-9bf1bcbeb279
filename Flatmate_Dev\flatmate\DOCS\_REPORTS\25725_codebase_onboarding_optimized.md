# FlatMate Codebase Onboarding Guide - AI-Optimized

**Target Audience**: AI developers and new team members  
**Reading Time**: 15-20 minutes  
**Focus**: Actionable understanding for immediate contribution

## Quick Start

```bash
# Run immediately
flatmate/.venv_fm313/Scripts/python.exe src/fm/main.py

# Run tests
flatmate/.venv_fm313/Scripts/python.exe tests/test_real_csvs.py
```

## Architecture at a Glance

```
flatmate/
├── src/fm/main.py              # Entry point
├── src/fm/module_coordinator.py # Module lifecycle
├── src/fm/core/               # Infrastructure (don't modify)
├── src/fm/modules/            # Your work happens here
│   ├── update_data/          # Data import module
│   ├── categorize/           # Transaction categorization
│   └── [feature-modules]/    # Add new features here
└── tests/                    # Always test your changes
```

## Key Patterns (Memorize These)

### 1. Module Pattern (Copy-Paste Template)
```python
# modules/your_feature/your_module.py
class YourModule(BaseModule):
    def setup(self):
        # Initialize UI components
        pass
    
    def show(self):
        # Display module
        pass
    
    def hide(self):
        # Hide module  
        pass
```

### 2. <PERSON> (Always Follow)
- **View**: PySide6 widgets only
- **Presenter**: Business logic here
- **Model**: Data structures only

### 3. Service Pattern (For Data Access)
```python
# services/your_service.py
class YourService:
    def get_data(self):
        # Database access
        pass
```

## Development Workflow

### 1. Find Your Module
Navigate to `src/fm/modules/[your_feature]/`

### 2. Understand Current State
```bash
# See what files exist
find src/fm/modules/update_data -name "*.py" | head -10
```

### 3. Make Changes
- **Views**: Look in `_view/` directories
- **Business Logic**: Look in `services/` directories
- **Data Models**: Look in `models/` directories

### 4. Test Immediately
```bash
# Run specific test
flatmate/.venv_fm313/Scripts/python.exe tests/test_your_feature.py
```

## Common Tasks

### Adding a New Feature Module
1. Copy existing module structure
2. Register in module_coordinator.py
3. Add tests
4. Run full test suite

### Modifying UI
1. Find relevant view file
2. Check presenter for business logic
3. Make changes
4. Test UI manually

### Adding Database Access
1. Create service in `services/`
2. Add repository in `repositories/`
3. Update presenter to use service
4. Add tests

## Critical Files to Know

| File | Purpose | Modify? |
|------|---------|---------|
| `main.py` | Entry point | No |
| `module_coordinator.py` | Module loading | Rarely |
| `*/_view/*.py` | UI components | Yes |
| `*/services/*.py` | Business logic | Yes |
| `*/models/*.py` | Data structures | Yes |
| `tests/*.py` | Tests | Always |

## Anti-Patterns (Don't Do These)

- **Don't** modify core infrastructure files
- **Don't** create new directory structures
- **Don't** add complex state management
- **Don't** modify logging configuration

## Quick Debugging

```python
# Add logging
from fm.core.services.logger import log
log.info("Your debug message")

# Check current state
print(f"Current state: {your_variable}")

# Test in isolation
if __name__ == "__main__":
    # Test your component directly
    pass
```

## Getting Help

1. **Check existing modules** - Copy patterns from working code
2. **Run tests** - They show expected behavior
3. **Use logging** - `log.info()` shows in console
4. **Ask specific questions** - "How do I add a button?" vs "It's broken"

## Red Flags (When to Ask)

- More than 10 files for a simple feature
- Complex state management with enums
- Deep directory nesting (4+ levels)
- Unclear component boundaries

## Success Checklist

Before submitting changes:
- [ ] Tests pass
- [ ] Code follows existing patterns
- [ ] No new dependencies added
- [ ] Changes are minimal and focused
- [ ] Documentation updated if needed

---

**Remember**: FlatMate rewards simple, consistent patterns over clever solutions. When in doubt, copy from existing working code.
