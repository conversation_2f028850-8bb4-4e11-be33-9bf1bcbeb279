---

# TEST_GUI Workflow Protocol
 Description: An AI-developer friendly protocol for prototyping the mock MVVP state machine in a GUI - optimized for iterative test cycles
---
*Updated: 2025-07-23*

## Document Purpose
This document defines the protocol for testing and iteratively improving the GUI prototype. It serves as the master reference for how test cycles should be conducted and documented.

## File Organization
- **Test Reports**: Use the template in `workflow_protocol_files/user_test_report_template.md` for all test sessions
- **Workflow Insights**: Record process improvements in `workflow_protocol_files/workflow_insights.md`
- **UI Implementation Insights**: Record UI/code insights in `insights_gained.md`
- **Actionable Tasks**: Extract concrete tasks to `test_tasks.md`

## Test Cycle Protocol
1. **Preparation**: Ensure prototype is running with latest code changes
2. **Testing**: Conduct test using the current prototype
3. **Documentation**: Fill out test report using the template with version number
4. **Task Extraction**: Distill actionable items into `test_tasks.md`
5. **Insight Capture**: 
   - Record workflow process insights in `workflow_insights.md`
   - Record UI implementation insights in `insights_gained.md`
6. **Protocol Review**: Update this protocol document if process improvements are identified
7. **Implementation Planning**: Create an implementation plan if changes are complex
8. ** Implementation**: 
   
   - Implement changes:
   - Update `changelog.md` with each modification
   - test and debug
   - once all is funcitonal: next step

9. **Verification Testing**: Run a full test cycle after all changes are complete

## Documentation Guidelines
- Keep all documents concise and actionable
- Use timestamps consistently across all documents
- Maintain clear separation between workflow insights and implementation insights
- Limit folder structure to no more than 5 file types per folder
- Create new organizational folders only when necessary (not speculatively)

---
*Review and update this protocol after significant workflow changes or every 3-5 test cycles.*