"""
Enums for Update Data module - GUI text constants.
Based on YOUR v1 specification.
"""

from enum import Enum


class SourceOptions(Enum):
    """Source selection options - matches GUI text exactly."""
    SELECT_FOLDER = "Select Folder"
    SELECT_FILES = "Select Files"
    ADD_FOLDER = "add folder"  # From YOUR spec row 10


class ArchiveOptions(Enum):
    """Archive/Save location options - matches GUI text exactly."""
    SAME_AS_SOURCE = "Same as source"
    SELECT_FOLDER = "Select folder..."


class UIStates(Enum):
    """UI states from YOUR specification."""
    INITIAL = "INITIAL"
    FOLDER_SELECTED = "Folder_selected"


class ComponentStates(Enum):
    """Component states from YOUR specification."""
    ACTIVE = "active"
    DISABLED = "disabled"
    DISPLAYED = "displayed"
    HIDDEN = "hidden"
    SELECTED = "Selected"


class BusinessEvents(Enum):
    """High-level business events for clean signal handling."""
    # Source events
    FOLDER_SELECTION_REQUESTED = "folder_selection_requested"
    FILES_SELECTION_REQUESTED = "files_selection_requested"
    ADD_FOLDER_REQUESTED = "add_folder_requested"
    
    # Archive events  
    ARCHIVE_SAME_AS_SOURCE = "archive_same_as_source"
    ARCHIVE_FOLDER_SELECTION_REQUESTED = "archive_folder_selection_requested"
    
    # Process events
    PROCESS_FILES_REQUESTED = "process_files_requested"
    UPDATE_DATABASE_TOGGLED = "update_database_toggled"
    CREATE_MASTER_TOGGLED = "create_master_toggled"
    
    # State change events
    SOURCE_CONFIGURED = "source_configured"
    ARCHIVE_CONFIGURED = "archive_configured"
    READY_TO_PROCESS = "ready_to_process"


class ComponentIDs(Enum):
    """Component IDs from YOUR state table."""
    SELECT_FILES_LABEL = "select_files_label"
    SELECT_FILES_OPTMENU = "select_files_optmenu"
    SELECT_FILES_BTN = "select_files_btn"
    SELECT_SAVE_LABEL = "select_save_label"
    SELECT_SAVE_OPTMENU = "select_save_optmenu"
    SELECT_SAVE_BTN = "select_save_btn"
    UPDATE_DB_CHECKBOX = "update_db_checkbox"
    CREATE_MASTER_CHECKBOX = "create_master_checkbox"
    PROCESS_BTN = "process_btn"
