"""
Guide Pane Widget for Update Data Module.

Adapted from existing StatusInfoWidget - provides contextual guidance messages.
"""

from PySide6.QtCore import Qt
from PySide6.QtGui import QFont
from PySide6.QtWidgets import QFrame, QLabel, QVBoxLayout, QWidget


class GuidePaneWidget(QWidget):
    """
    Contextual guidance widget for Update Data GUI.
    Based on existing StatusInfoWidget pattern.
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()

    def _setup_ui(self):
        """Set up the UI components following existing pattern."""
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)

        # Guide frame - following StatusInfoWidget pattern
        self.guide_frame = QFrame()
        self.guide_frame.setFrameShape(QFrame.Shape.Box)
        self.guide_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #4CAF50;
                border-radius: 4px;
                background-color: #2a2a2a;
                padding: 8px;
            }
        """)

        frame_layout = QVBoxLayout(self.guide_frame)
        frame_layout.setContentsMargins(8, 8, 8, 8)

        # Guide message label
        self.message_label = QLabel("Select source files to begin")
        self.message_label.setWordWrap(True)
        self.message_label.setFont(QFont("Arial", 9))
        self.message_label.setStyleSheet("color: #aaa; font-style: italic;")
        frame_layout.addWidget(self.message_label)

        layout.addWidget(self.guide_frame)

    def display(self, message: str):
        """Display a message in the guide pane."""
        print(f"[GUIDE_PANE] {message}")  # Debug logging
        self.message_label.setText(message)

    def show_info(self, message: str, color: str = '#aaa'):
        """Display an informational message with color."""
        self.message_label.setText(message)
        self.message_label.setStyleSheet(f"color: {color}; font-style: italic;")

    def show_warning(self, message: str):
        """Display a warning message."""
        self.show_info(f"⚠️ {message}", 'orange')

    def show_error(self, message: str):
        """Display an error message."""
        self.show_info(f"❌ {message}", 'red')

    def clear_message(self):
        """Clear the current message."""
        self.message_label.setText("Select source files to begin")
