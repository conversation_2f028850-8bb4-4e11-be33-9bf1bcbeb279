import sys
from enum import Enum, auto
from PySide6.QtCore import QObject, Signal, Slot, Qt
from PySide6.QtWidgets import (
    QApplication,
    QMainWindow,
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QPushButton,
    QComboBox,
    QCheckBox,
    QLabel,
    QTreeView,
    QTextEdit,
    QSplitter,
    QFileDialog,
    QMessageBox
)
from PySide6.QtGui import QStandardItemModel, QStandardItem
import logging
import csv
from pathlib import Path

# Basic logger for prototype
logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')
log = logging.getLogger()

# --- Placeholder Shared Components ---
# In a real app, these would be in a shared library. For the prototype,
# we define simple versions here.

class HeadingLabel(QLabel):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        font = self.font()
        font.setPointSize(16)
        font.setBold(True)
        self.setFont(font)

class SubheadingLabel(QLabel):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        font = self.font()
        font.setPointSize(12)
        font.setBold(True)
        self.setFont(font)

class OptionMenuWithLabel(QWidget):
    def __init__(self, label_text, options, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        self.label = SubheadingLabel(label_text)
        self.combo_box = QComboBox()
        self.combo_box.addItems(options)
        layout.addWidget(self.label)
        layout.addWidget(self.combo_box)

# --- State Engine ---
# Reads a CSV file to define UI states and transitions.

class StateEngine(QObject):
    def __init__(self, csv_path, parent=None):
        super().__init__(parent)
        self.config = {}
        self._load_config(csv_path)

    def _load_config(self, csv_path):
        log.info(f"Loading state configuration from: {csv_path}")
        try:
            with open(csv_path, mode='r', encoding='utf-8') as infile:
                reader = csv.DictReader(infile)
                for row in reader:
                    component_id = row.get('component_id')
                    if not component_id or component_id.startswith('#'):
                        continue
                    self.config[component_id] = {
                        'state_initial': row.get('state_initial'),
                        'text_initial': row.get('text_initial'),
                        'text_folder_selected': row.get('text_folder_selected'),
                    }
        except FileNotFoundError:
            log.error(f"State configuration file not found at {csv_path}")
        except Exception as e:
            log.error(f"Error loading state configuration: {e}")

    def get_initial_config(self, component_id):
        return self.config.get(component_id, {})

# --- Mock ViewModel ---
# This class mimics the real SourceSelectionViewModel's interface.
# It now uses the StateEngine for its configuration.

class MockSourceSelectionViewModel(QObject):
    # ... (rest of the class remains the same until the state definitions)
    """
    A mock ViewModel to prototype the state machine logic for source selection.
    """
    # Signals to update the UI
    update_dropdown = Signal(str, list)
    enable_dropdown = Signal(bool) # New signal
    update_select_button = Signal(str, bool)
    update_process_button = Signal(bool)
    update_auto_import_checkbox = Signal(bool, bool)
    update_info_pane = Signal(str)
    state_changed_info = Signal(str)  # To display the current state in the UI

    # Signals to request actions from the View
    open_folder_dialog_requested = Signal()
    open_files_dialog_requested = Signal()

    class UIState(Enum):
        INITIAL = auto()
        FOLDER_MODE = auto()
        FILES_MODE = auto()
        SOURCE_SELECTED = auto()

    def __init__(self, state_engine, parent=None):
        super().__init__(parent)
        self.state_engine = state_engine
        self._state = None
        self.set_state(self.UIState.INITIAL)

    def set_state(self, new_state):
        if self._state == new_state:
            return
        log.info(f"[STATE CHANGE] From {self._state} -> {new_state}")
        self.state_changed_info.emit(f"State: {new_state.name}")
        self._state = new_state
        self._update_ui_for_state()

    def _update_ui_for_state(self):
        """Central logic to update the UI based on the current state's config."""
        # Get widget configs from the state engine
        source_menu_cfg = self.state_engine.get_initial_config('source_menu')
        source_select_btn_cfg = self.state_engine.get_initial_config('source_select_btn')
        process_btn_cfg = self.state_engine.get_initial_config('process_btn')
        info_pane_cfg = self.state_engine.get_initial_config('monitoring_status') # Using this for info text

        # Determine properties based on state
        is_initial = self._state in [self.UIState.INITIAL, self.UIState.FOLDER_MODE, self.UIState.FILES_MODE]
        is_source_selected = self._state == self.UIState.SOURCE_SELECTED

        dropdown_enabled = True
        select_btn_text = source_select_btn_cfg.get('text_folder_selected') if is_source_selected else source_select_btn_cfg.get('text_initial')
        select_btn_enabled = True
        process_btn_enabled = process_btn_cfg.get('state_initial') == 'disabled' and is_source_selected
        info_text = info_pane_cfg.get('text_folder_selected') if is_source_selected else info_pane_cfg.get('text_initial')
        
        if self._state == self.UIState.FILES_MODE:
            select_btn_text = "Select Files..."

        # Emit signals to update UI
        self.enable_dropdown.emit(dropdown_enabled)
        self.update_select_button.emit(select_btn_text, select_btn_enabled)
        self.update_process_button.emit(process_btn_enabled)
        self.update_info_pane.emit(info_text)
        self.update_auto_import_checkbox.emit(False, False) # Keep hidden

    @Slot(str)
    def on_dropdown_selection_changed(self, text):
        log.info(f"Dropdown changed to: {text}")
        if "Folder" in text:
            self.set_state(self.UIState.FOLDER_MODE)
        elif "File" in text:
            self.set_state(self.UIState.FILES_MODE)
        else:
            self.set_state(self.UIState.INITIAL)

    @Slot()
    def on_select_button_clicked(self):
        log.info("Select button clicked")
        if self._state in [self.UIState.INITIAL, self.UIState.FOLDER_MODE, self.UIState.SOURCE_SELECTED]:
            self.open_folder_dialog_requested.emit()
        elif self._state == self.UIState.FILES_MODE:
            self.open_files_dialog_requested.emit()

    @Slot(bool)
    def on_auto_import_toggled(self, checked):
        log.info(f"Auto-import toggled: {checked}")

    def on_folder_selected(self, path):
        log.info(f"Folder selected in ViewModel: {path}")
        self.set_state(self.UIState.SOURCE_SELECTED)

    def on_files_selected(self, files):
        log.info(f"{len(files)} files selected in ViewModel")
        self.set_state(self.UIState.SOURCE_SELECTED)


# --- Main Application Window ---

class MockUpdateDataWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Update Data - Prototype")
        self.setGeometry(100, 100, 800, 600)

        # State Engine
        schema_path = Path(__file__).parent / "state_schema" / "machine_readable_schema.csv"
        self.state_engine = StateEngine(schema_path, self)

        # Viewmodel
        self.view_model = MockSourceSelectionViewModel(self.state_engine, self)

        self._init_ui()
        self._connect_signals()

        # Ensure state label is set on startup
        self.view_model.state_changed_info.emit(f"State: {self.view_model._state.name}")
        # Force an initial UI update to reflect the ViewModel's starting state
        self.view_model._update_ui_for_state()

    def _init_ui(self):
        # Main container widget
        main_widget = QWidget()
        main_layout = QHBoxLayout(main_widget)
        self.setCentralWidget(main_widget)

        # Splitter for layout
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # Left Panel
        left_panel = self._create_left_panel()
        splitter.addWidget(left_panel)

        # Center Panel
        center_panel = self._create_center_panel()
        splitter.addWidget(center_panel)

        splitter.setSizes([250, 550])

    def _create_left_panel(self):
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Title
        self.title = HeadingLabel("Update Data")
        layout.addWidget(self.title)

        # Meta Info Heading for State
        self.state_info_label = QLabel("State: --")
        font = self.state_info_label.font()
        font.setItalic(True)
        self.state_info_label.setFont(font)
        self.state_info_label.setStyleSheet("color: grey; margin-bottom: 10px;")
        layout.addWidget(self.state_info_label)

        # Source Files Section
        source_options = ["-- Select --", "Import Folder", "Import File(s)"]
        self.source_menu = OptionMenuWithLabel("Import data from:", source_options)
        layout.addWidget(self.source_menu)

        self.source_select_btn = QPushButton("Select...")
        layout.addWidget(self.source_select_btn)

        self.auto_import_checkbox = QCheckBox("Auto-Import new files")
        layout.addWidget(self.auto_import_checkbox)

        # Process Section
        self.process_label = SubheadingLabel("2. Process")
        layout.addWidget(self.process_label)

        self.process_btn = QPushButton("Process")
        self.process_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        layout.addWidget(self.process_btn)

        layout.addStretch()

        # Reset Button
        self.reset_btn = QPushButton("Reset")
        layout.addWidget(self.reset_btn)
        return panel

    def _create_center_panel(self):
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Info Pane (moved to top)
        self.info_pane = QTextEdit()
        self.info_pane.setReadOnly(True)
        self.info_pane.setFixedHeight(60) # Reduced height slightly
        self.info_pane.setStyleSheet("border: 1px solid lightgrey; background-color: #f0f0f0;")
        layout.addWidget(self.info_pane)

        # File Tree
        self.file_tree = QTreeView()
        self.file_tree_model = QStandardItemModel()
        self.file_tree.setModel(self.file_tree_model)
        self.file_tree_model.setHorizontalHeaderLabels(['File/Folder', 'Status'])
        layout.addWidget(self.file_tree)

        return panel

    def _connect_signals(self):
        # Connect UI controls to ViewModel slots
        self.source_menu.combo_box.currentTextChanged.connect(self.view_model.on_dropdown_selection_changed)
        self.source_select_btn.clicked.connect(self.view_model.on_select_button_clicked)
        self.auto_import_checkbox.stateChanged.connect(lambda state: self.view_model.on_auto_import_toggled(state == Qt.Checked))

        # Connect ViewModel signals to UI update slots
        self.view_model.update_select_button.connect(self._update_source_select_button)
        self.view_model.update_process_button.connect(self._update_process_button)
        self.view_model.update_auto_import_checkbox.connect(self.auto_import_checkbox.setVisible)
        self.view_model.update_info_pane.connect(self.info_pane.setText)
        self.view_model.state_changed_info.connect(self.state_info_label.setText)
        self.view_model.enable_dropdown.connect(self.source_menu.combo_box.setEnabled)
        self.view_model.update_dropdown.connect(self._update_source_dropdown)

        # Connect ViewModel action requests to handlers in the View
        self.view_model.open_folder_dialog_requested.connect(self.handle_open_folder_dialog)
        self.view_model.open_files_dialog_requested.connect(self.handle_open_files_dialog)

        # Connect local buttons
        self.reset_btn.clicked.connect(self._on_reset_clicked)

    @Slot(str, bool)
    def _update_source_select_button(self, text, enabled):
        self.source_select_btn.setText(text)
        self.source_select_btn.setEnabled(enabled)
        # Improve visual distinction for button state
        if enabled:
            self.source_select_btn.setStyleSheet("background-color: #e0e0e0; font-weight: bold;")
        else:
            self.source_select_btn.setStyleSheet("background-color: #f0f0f0; color: #a0a0a0;")

    @Slot(str, list)
    def _update_source_dropdown(self, text, options):
        # We only care about setting the text in this specific case
        if text:
            self.source_menu.combo_box.setCurrentText(text)

    @Slot()
    def handle_open_folder_dialog(self):
        folder_path = QFileDialog.getExistingDirectory(self, "Select Import Folder")
        if folder_path:
            log.info(f"Folder selected in View: {folder_path}")
            self.view_model.on_folder_selected(folder_path)
            # Update tree view with truncated path for display
            self.file_tree_model.clear()
            self.file_tree_model.setHorizontalHeaderLabels(['File/Folder', 'Status'])
            
            # Store full path but display truncated version
            display_path = folder_path
            if len(folder_path) > 50:  # Truncate if too long
                display_path = folder_path[:20] + "..." + folder_path[-27:]
                
            item = QStandardItem(display_path)
            item.setToolTip(folder_path)  # Full path in tooltip
            status_item = QStandardItem("Selected")
            self.file_tree_model.appendRow([item, status_item])
            
            # Add option to set as default in info pane
            current_info = self.info_pane.toPlainText()
            self.info_pane.setText(current_info + "\n\nTip: You can set this as your default import folder.")

    @Slot()
    def handle_open_files_dialog(self):
        files, _ = QFileDialog.getOpenFileNames(self, "Select Files")
        if files:
            log.info(f"{len(files)} files selected in View")
            self.view_model.on_files_selected(files)
            # Update tree view
            self.file_tree_model.clear()
            self.file_tree_model.setHorizontalHeaderLabels(['File/Folder', 'Status'])
            for file_path in files:
                item = QStandardItem(file_path)
                status_item = QStandardItem("Selected")
                self.file_tree_model.appendRow([item, status_item])

    @Slot(bool)
    def _update_process_button(self, enabled):
        self.process_btn.setEnabled(enabled)
        if enabled:
            self.process_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        else:
            self.process_btn.setStyleSheet("background-color: #a0a0a0; color: #e0e0e0;")
            
    @Slot()
    def _on_reset_clicked(self):
        log.info("--- RESETTING UI ---")
        self.file_tree_model.clear()
        self.file_tree_model.setHorizontalHeaderLabels(['File/Folder', 'Status'])
        self.view_model.set_state(self.view_model.UIState.INITIAL)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MockUpdateDataWindow()
    window.show()
    sys.exit(app.exec())

