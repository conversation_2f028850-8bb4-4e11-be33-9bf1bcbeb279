{"mcpServers": {"puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "TAV2": {"command": "npx", "args": ["-y", "tavily-mcp@0.1.3"], "env": {"TAVILY_API_KEY": "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"}}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"]}}}