# Update Data Module UI Architecture

## Current Structure
The Update Data module currently uses a three-panel layout:
- Left panel: Control buttons and options
- Center panel: Main content display
- Right panel: Additional information (when needed)

The module operates in different modes (process mode, exit mode) that change the available actions and UI elements.

## Identified Issues
1. Navigation between modules requires returning to the home screen
2. Mode transitions lack contextual suggestions for next steps
3. The naming and purpose of buttons can be confusing in different contexts

## Proposed Improvements

### Core Philosophy: Simple + Contextual
Maintain a simple, focused interface while adding contextual navigation options that give users flexibility without overwhelming them.

### Specific Recommendations

#### 1. Enhanced Module-Based Navigation
- Keep the current module-based approach for clarity
- Add a persistent "quick navigation" bar for direct access to main modules
- Each module maintains its focused, context-specific interface

#### 2. Contextual Suggestions
After completing actions, suggest logical next steps:
- After processing data: "View in Accounts" or "Process More Files"
- When viewing results: Options to export, modify, or continue to analysis

#### 3. Mode Refinements
Replace the simple process/exit modes with more nuanced states:
- **Selection Mode**: Focus on selecting source files and destination
- **Processing Setup Mode**: Preview and configure processing options
- **Results Mode**: Show processing results with options for next steps
- **Viewing Mode**: For examining existing data with analysis tools

#### 4. Button Clarity
- Use specific action verbs on buttons rather than generic terms
- Provide visual cues about button functions in different contexts
- Ensure cancel/exit buttons have consistent behavior

## Implementation Approach
1. Complete the current code reorganization for better maintainability
2. Implement the navigation bar as a shared component
3. Enhance the mode system to support contextual suggestions
4. Update button labels and behaviors for clarity

## User Flow Examples

### Example 1: New Data Import
1. User selects Update Data from home
2. In Selection Mode, user chooses files and save location
3. After processing, Results Mode shows success and offers:
   - "View in Accounts" (direct link to accounts module)
   - "Process More Files" (reset to Selection Mode)
   - "Return to Home"

### Example 2: Viewing Existing Data
1. User opens existing data file
2. Viewing Mode shows data with analysis options
3. User can choose to:
   - "Update with New Transactions"
   - "Export/Share"
   - "Modify Categories"

This approach respects the original vision of simplicity while adding thoughtful connections between modules based on natural user flows.
