"""
Update Data ViewModel - Signal translation layer.
Translates low-level widget signals to high-level business signals.
Integrates with state engine based on YOUR v1 specification.
"""

from PySide6.QtCore import QObject, Signal
from .enums import SourceOptions, ArchiveOptions, BusinessEvents, UIStates, ComponentIDs
from .state_engine import StateEngine


class UpdateDataViewModel(QObject):
    """
    ViewModel that translates widget signals to business logic signals.
    Maintains state using YOUR state table specification.
    """
    
    # High-level business signals
    folder_selection_requested = Signal()
    files_selection_requested = Signal()
    add_folder_requested = Signal()
    archive_same_as_source = Signal()
    archive_folder_selection_requested = Signal()
    process_files_requested = Signal()
    update_database_toggled = Signal(bool)
    create_master_toggled = Signal(bool)
    
    # State change signals
    source_configured = Signal(str)  # Emits folder path
    archive_configured = Signal(str)  # Emits archive path
    ready_to_process = Signal(bool)  # Emits ready state
    
    def __init__(self, parent=None):
        """Initialize the ViewModel with state engine."""
        super().__init__(parent)
        
        # Initialize state engine with YOUR specification
        self.state_engine = StateEngine()
        self.current_state = UIStates.INITIAL.value
        
        # Track current selections
        self.source_path = None
        self.archive_path = None
        self.update_database = True  # Selected by default per YOUR spec
        self.create_master = False   # Hidden by default per YOUR spec
        
    def handle_source_option_changed(self, option_text: str):
        """
        Translate source option changes to business signals.
        Based on YOUR v1 specification rows 8-9.
        """
        if option_text == SourceOptions.SELECT_FOLDER.value:
            self.folder_selection_requested.emit()
        elif option_text == SourceOptions.SELECT_FILES.value:
            self.files_selection_requested.emit()
        elif option_text == SourceOptions.ADD_FOLDER.value:
            self.add_folder_requested.emit()
            
    def handle_archive_option_changed(self, option_text: str):
        """
        Translate archive option changes to business signals.
        Based on YOUR v1 specification rows 15-16.
        """
        if option_text == ArchiveOptions.SAME_AS_SOURCE.value:
            self.archive_same_as_source.emit()
        elif option_text == ArchiveOptions.SELECT_FOLDER.value:
            self.archive_folder_selection_requested.emit()
            
    def handle_source_select_clicked(self, current_option: str):
        """
        Handle source select button clicks.
        Emits appropriate signal based on current option.
        """
        if current_option == SourceOptions.SELECT_FOLDER.value:
            self.folder_selection_requested.emit()
        elif current_option == SourceOptions.SELECT_FILES.value:
            self.files_selection_requested.emit()
            
    def handle_archive_select_clicked(self, current_option: str):
        """
        Handle archive select button clicks.
        """
        if current_option == ArchiveOptions.SELECT_FOLDER.value:
            self.archive_folder_selection_requested.emit()
            
    def handle_process_clicked(self):
        """Handle process button clicks."""
        self.process_files_requested.emit()
        
    def handle_update_database_changed(self, checked: bool):
        """Handle database update checkbox changes."""
        self.update_database = checked
        self.update_database_toggled.emit(checked)
        
    def handle_create_master_changed(self, checked: bool):
        """Handle create master checkbox changes."""
        self.create_master = checked
        self.create_master_toggled.emit(checked)
        
    # State management methods based on YOUR specification
    
    def set_source_configured(self, path: str):
        """
        Set source as configured and update state.
        Triggers state transition from INITIAL to FOLDER_SELECTED.
        """
        self.source_path = path
        self.current_state = UIStates.FOLDER_SELECTED.value
        self.source_configured.emit(path)
        
        # Update UI state based on YOUR specification
        self._update_ui_for_folder_selected()
        
    def set_archive_configured(self, path: str):
        """Set archive location and check if ready to process."""
        self.archive_path = path
        self.archive_configured.emit(path)
        self._check_ready_to_process()
        
    def _update_ui_for_folder_selected(self):
        """
        Update UI when folder is selected.
        Based on YOUR specification row 14: "On source files folder set"
        """
        # Enable archive section (was disabled initially per YOUR spec row 18)
        # This will be handled by the view when it receives the source_configured signal
        pass
        
    def _check_ready_to_process(self):
        """
        Check if all requirements are met for processing.
        Based on YOUR specification logic.
        """
        ready = (
            self.source_path is not None and 
            self.archive_path is not None
        )
        self.ready_to_process.emit(ready)
        
    def get_state_for_component(self, component_id: str):
        """
        Get the current state for a component from the state engine.
        Integrates with YOUR state table.
        """
        if component_id in self.state_engine.components:
            component = self.state_engine.components[component_id]
            return {
                'state': component.state_initial,
                'text': component.text_initial,
                'text_folder_selected': component.text_folder_selected
            }
        return None
        
    def should_component_be_enabled(self, component_id: str) -> bool:
        """
        Determine if component should be enabled based on current state.
        Uses YOUR state table logic.
        """
        if component_id == ComponentIDs.SELECT_SAVE_BTN.value:
            # Disabled initially, enabled when source is configured
            return self.current_state == UIStates.FOLDER_SELECTED.value
        elif component_id == ComponentIDs.PROCESS_BTN.value:
            # Enabled when both source and archive are configured
            return self.source_path is not None and self.archive_path is not None
        return True
        
    def get_display_text_for_component(self, component_id: str) -> str:
        """
        Get the display text for a component based on current state.
        Uses YOUR state table text_folder_selected column.
        """
        component_state = self.get_state_for_component(component_id)
        if component_state and self.current_state == UIStates.FOLDER_SELECTED.value:
            return component_state.get('text_folder_selected', component_state['text'])
        elif component_state:
            return component_state['text']
        return ""
