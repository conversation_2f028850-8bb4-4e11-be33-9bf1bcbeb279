
# init sequence

## [main.py](/flatmate/main.py)  

# [config.py](/flatmate/src/fm/core/config/config.py)
    [defaults_yaml]
  - [user_prefs_yaml](/Users/<USER>/.flatmate/preferences.yaml) - not in workspace folder, link doesn't work


# === GUI ===
[gui_folder](/flatmate/src/fm/gui)
[components/main_window](/flatmate/src/fm/gui/components/main_window)
[components/shared](/flatmate/src/fm/gui/components/shared)


## [Main_Window](/flatmate/src/fm/gui/main_window.py)
  # components 
  [Icons](/flatmate/src/fm/gui/icons/)
  [Nav_Pane](/flatmate/src/fm/gui/components/nav_pane.py)
  [Nav_Pane_Icons](/flatmate/src/fm/gui/icons/nav_pane/)

# === Modules === 
## ud_data_folder
[ud_data](/flatmate/src/fm/modules/update_data/)
[ud_presenter](/flatmate/src/fm/modules/update_data/ud_presenter.py)
[ud_view](/flatmate/src/fm/modules/update_data/_view.py)
[UD_Right_Panel](/flatmate/src/fm/modules/update_data/_view/right_panel/_panel_manager.py)

# === Other ===
[Module_Co-ordinator](/flatmate/src/fm/modules/_module_co-ordinator)

[APP_BACK_UPS](/z_APP_BACK_UPS)
