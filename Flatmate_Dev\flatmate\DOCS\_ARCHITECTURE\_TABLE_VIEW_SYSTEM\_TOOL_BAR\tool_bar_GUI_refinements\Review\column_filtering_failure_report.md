# COLUMN FILTERING FAILURE REPORT

## Summary
The show/hide columns feature in the new table view toolbar is displaying internal/system columns (such as database IDs and flags) to the user. This is a regression and violates the intended architecture, which requires that only user-facing columns (as defined by the `Columns` constants) are shown.

## Root Cause Analysis
- The toolbar's `set_columns()` method attempts to filter out internal columns using a hardcoded set of names (`'db_uid'`, `'source_uid'`, `'is_deleted'`).
- The filter only works if the incoming column names match these strings exactly (case-insensitive, but not tolerant of variants or display names).
- In practice, the columns passed to the toolbar are often raw DataFrame columns, display names, or otherwise not normalised, so the filter fails.
- Critically, **the toolbar and related UI code do NOT consistently use the canonical column constants from the `Columns` class/module**. This breaks the contract and allows system/internal columns to leak into the UI.

## Architectural Violation
- All column logic—filtering, display mapping, and system/user distinction—**must** be centralised in the `Columns` class/module.
- Any code that constructs or filters a column list for user display must use only the canonical constants and helpers from `Columns`.
- Hardcoding column names or duplicating filtering logic is brittle and error-prone.

## Recommendation
- Refactor all toolbar and table UI logic to:
  - Accept only canonical column names from `Columns`.
  - Use `Columns` helpers to map to display names and to filter out system/internal columns.
  - Remove any hardcoded or duplicated filtering logic from UI components.
- Add tests to ensure that no system/internal columns can appear in any user-facing column list.

## Status
- This is a critical maintainability and UX issue. Immediate attention is required to restore architectural discipline and prevent further regressions.

---
*Generated automatically. See code and commit history for full context.*
