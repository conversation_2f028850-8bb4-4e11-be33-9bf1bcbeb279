"""
Guide Pane Widget for Update Data Module.

Provides contextual guidance messages to help users through the workflow.
"""

from PySide6.QtWidgets import QWidget, QLabel, QVBoxLayout
from PySide6.QtCore import Qt


class GuidePaneWidget(QWidget):
    """
    Contextual guidance widget for Update Data GUI.
    Displays helpful messages to guide user through the workflow.
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
    
    def _setup_ui(self):
        """Set up the UI components."""
        layout = QVBoxLayout(self)
        
        self.message_label = QLabel("Select source files to begin")
        self.message_label.setWordWrap(True)
        self.message_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        layout.addWidget(self.message_label)
        
    def display(self, message: str):
        """Display a message in the guide pane."""
        self.message_label.setText(message)
