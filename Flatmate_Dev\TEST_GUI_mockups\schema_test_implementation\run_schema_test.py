#!/usr/bin/env python3
"""
Schema Test Runner

Simple script to run the schema test GUI for validating the MVP state schema
implementation. This script handles the Python path setup and launches the
test GUI with proper error handling.

Usage:
    python run_schema_test.py
    
    Or from the flatmate directory with venv activated:
    python src/fm/modules/update_data/_view/run_schema_test.py
"""

import sys
import os
from pathlib import Path

def setup_python_path():
    """Setup Python path to include the project root."""
    # Get the project root (5 levels up from this file)
    current_file = Path(__file__).resolve()
    project_root = current_file.parent.parent.parent.parent.parent
    
    # Add to Python path if not already there
    project_root_str = str(project_root)
    if project_root_str not in sys.path:
        sys.path.insert(0, project_root_str)
    
    print(f"Project root: {project_root}")
    print(f"Python path updated: {project_root_str in sys.path}")
    
    return project_root

def check_dependencies():
    """Check if required dependencies are available."""
    missing_deps = []
    
    try:
        import PySide6
        print(f"✓ PySide6 version: {PySide6.__version__}")
    except ImportError:
        missing_deps.append("PySide6")
    
    try:
        from fm.gui._shared_components.widgets import HeadingLabel
        print("✓ Shared components available")
    except ImportError as e:
        print(f"✗ Shared components not found: {e}")
        missing_deps.append("fm.gui._shared_components")
    
    if missing_deps:
        print(f"\n❌ Missing dependencies: {', '.join(missing_deps)}")
        print("Please ensure you're running from the correct directory with the virtual environment activated.")
        return False
    
    return True

def run_test_gui():
    """Run the schema test GUI."""
    try:
        from test_schema_gui import SchemaTestGUI
        from PySide6.QtWidgets import QApplication
        
        print("Starting Schema Test GUI...")
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("Update Data Schema Test")
        app.setApplicationVersion("1.0")
        
        # Create and show the test window
        window = SchemaTestGUI()
        window.show()
        
        print("✓ GUI launched successfully")
        print("✓ Use the test controls on the right to simulate state changes")
        print("✓ Check the event log for detailed behavior tracking")
        
        # Run the application
        return app.exec()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all required modules are available.")
        return 1
    except Exception as e:
        print(f"❌ Error launching GUI: {e}")
        return 1

def main():
    """Main entry point."""
    print("=" * 60)
    print("Update Data Module - Schema Test Runner")
    print("=" * 60)
    
    # Setup environment
    project_root = setup_python_path()
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    # Check if we're in the right directory
    expected_cwd = project_root / "flatmate"
    current_cwd = Path.cwd()
    
    if current_cwd != expected_cwd:
        print(f"\n⚠️  Warning: Current directory is {current_cwd}")
        print(f"   Expected directory is {expected_cwd}")
        print("   This might cause import issues.")
    
    # Check for virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✓ Virtual environment detected")
    else:
        print("⚠️  No virtual environment detected - this might cause issues")
    
    print("\n" + "-" * 40)
    print("Launching Schema Test GUI...")
    print("-" * 40)
    
    # Run the test GUI
    exit_code = run_test_gui()
    
    print("\n" + "-" * 40)
    print(f"Test GUI exited with code: {exit_code}")
    print("-" * 40)
    
    return exit_code

if __name__ == "__main__":
    sys.exit(main())
